RewriteEngine On

# Przekieruj wszystkie żądania do index.php (kompatybilność z LiteSpeed)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/admin/index\.php$
RewriteCond %{REQUEST_URI} !^/api/index\.php$
RewriteCond %{REQUEST_URI} !^/display/index\.php$
RewriteRule ^(.*)$ index.php [QSA,L]

# Bezpieczeństwo - ukryj pliki konfiguracyjne
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# Bezpieczeństwo - nie pozwalaj na dostęp do plików PHP w uploads
<Directory "uploads">
    php_flag engine off
</Directory>

# Typy MIME dla plików video
AddType video/mp4 .mp4
AddType video/webm .webm
AddType video/ogg .ogv

# Nagłówki CORS dla plików video
<FilesMatch "\.(mp4|webm|ogv)$">
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, HEAD"
    Header set Access-Control-Allow-Headers "Range"
</FilesMatch> 