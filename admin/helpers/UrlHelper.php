<?php

// Załaduj konfigurację jeśli nie została załadowana
if (!defined('KTOOSTATNI_CONFIG')) {
    require_once __DIR__ . '/../../config.php';
}

class UrlHelper {
    /**
     * Generuje URL z prefiksem aplikacji
     */
    public static function url($path = '') {
        $prefix = ADMIN_URL;

        // Usuń ukośnik z początku jeśli istnieje
        $path = ltrim($path, '/');

        // Jeś<PERSON> jest pusta, zwróć tylko prefiks
        if (empty($path)) {
            return $prefix;
        }

        return $prefix . '/' . $path;
    }

    /**
     * Generuje URL dla assetów (CSS, JS, obrazy)
     */
    public static function asset($path) {
        $prefix = ADMIN_URL;

        // Usuń ukośnik z początku jeśli istnieje
        $path = ltrim($path, '/');

        return $prefix . '/assets/' . $path;
    }

    /**
     * Generuje URL dla uploadów
     */
    public static function upload($path) {
        // Usuń ukośnik z początku jeśli istnieje
        $path = ltrim($path, '/');

        return '/uploads/' . $path;
    }

    /**
     * Sprawdza czy aktualny URL jest aktywny
     */
    public static function isActive($path) {
        $currentUri = $_SERVER['REQUEST_URI'] ?? '';
        $prefix = ADMIN_URL;

        // Usuń prefiks z aktualnego URI
        if (strpos($currentUri, $prefix) === 0) {
            $currentUri = substr($currentUri, strlen($prefix));
        }

        // Usuń ukośnik z początku
        $currentUri = ltrim($currentUri, '/');
        $path = ltrim($path, '/');

        // Sprawdź czy ścieżka się zgadza
        if ($path === '') {
            return $currentUri === '' || $currentUri === 'index.php';
        }

        return strpos($currentUri, $path) === 0;
    }
}
