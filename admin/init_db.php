<?php

/**
 * Inicjalizacja bazy danych systemu KtoOstatni
 * Zawiera strukturę tabel i dane początkowe (oprócz umówionych wizyt)
 */

// Załaduj konfigurację jeśli nie została załadowana
if (!defined('KTOOSTATNI_CONFIG')) {
    require_once __DIR__ . '/../config.php';
}

// Definicja schematu bazy danych
$database_schema = <<<SQL
-- Tabela użytkowników
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role TEXT NOT NULL,
    company_name VARCHAR(100),
    balance DECIMAL(10,2) DEFAULT 0,
    rate_per_second DECIMAL(5,4) DEFAULT 0.0001,
    is_active INTEGER DEFAULT 1,
    last_activity DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> kategorii kampanii
CREATE TABLE IF NOT EXISTS categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Tabela kampanii reklamowych
CREATE TABLE IF NOT EXISTS campaigns (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    advertiser_id INTEGER NOT NULL,
    category_id INTEGER,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    media_type TEXT NOT NULL,
    media_url VARCHAR(255) NOT NULL,
    youtube_id VARCHAR(20),
    duration INTEGER DEFAULT 30,
    budget DECIMAL(10,2) NOT NULL,
    spent DECIMAL(10,2) DEFAULT 0,
    rate_per_second DECIMAL(5,4) DEFAULT 0.0001,
    max_frequency_per_hour INTEGER DEFAULT 0,
    start_date DATETIME,
    end_date DATETIME,
    is_active INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (advertiser_id) REFERENCES users(id),
    FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- Tabela wyświetleń reklam
CREATE TABLE IF NOT EXISTS ad_views (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    campaign_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    duration_seconds INTEGER NOT NULL,
    cost DECIMAL(10,4) NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id),
    FOREIGN KEY (client_id) REFERENCES users(id)
);

-- Tabela monitorów/TV klientów
CREATE TABLE IF NOT EXISTS client_displays (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    display_code VARCHAR(6) UNIQUE,
    is_online INTEGER DEFAULT 0,
    last_heartbeat DATETIME,
    queue_system_enabled INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES users(id)
);

-- Tabela przypisań kampanii do klientów
CREATE TABLE IF NOT EXISTS campaign_assignments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    campaign_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    is_accepted INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id),
    FOREIGN KEY (client_id) REFERENCES users(id)
);

-- Tabela automatycznej akceptacji kategorii przez klientów
CREATE TABLE IF NOT EXISTS campaign_auto_accept (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    category_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES users(id),
    FOREIGN KEY (category_id) REFERENCES categories(id),
    UNIQUE(client_id, category_id)
);

-- Tabela konfiguracji systemu kolejkowego dla klientów
CREATE TABLE IF NOT EXISTS queue_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    is_enabled INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES users(id)
);

-- Tabela lekarzy w systemie kolejkowym
CREATE TABLE IF NOT EXISTS queue_doctors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    photo_url VARCHAR(255),
    specialization VARCHAR(200),
    access_code VARCHAR(12) UNIQUE,
    active INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    default_room_id INTEGER,
    FOREIGN KEY (client_id) REFERENCES users(id)
);

-- Tabela sal w systemie kolejkowym (rozszerzona o lekarza)
CREATE TABLE IF NOT EXISTS queue_rooms (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    doctor_id INTEGER,
    room_number VARCHAR(20),
    active INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES users(id),
    FOREIGN KEY (doctor_id) REFERENCES queue_doctors(id)
);

-- Tabela wizyt w systemie kolejkowym (zamiast numerów)
CREATE TABLE IF NOT EXISTS queue_appointments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    room_id INTEGER NOT NULL,
    doctor_id INTEGER,
    appointment_time TIME NOT NULL,
    appointment_date DATE,
    patient_name VARCHAR(200),
    status TEXT DEFAULT 'waiting', -- 'waiting', 'current', 'completed', 'cancelled'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    called_at DATETIME,
    completed_at DATETIME,
    FOREIGN KEY (client_id) REFERENCES users(id),
    FOREIGN KEY (room_id) REFERENCES queue_rooms(id),
    FOREIGN KEY (doctor_id) REFERENCES queue_doctors(id)
);

-- Tabela numerów kolejkowych (zachowana dla kompatybilności wstecznej)
CREATE TABLE IF NOT EXISTS queue_numbers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    room_id INTEGER NOT NULL,
    number INTEGER NOT NULL,
    status TEXT DEFAULT 'waiting', -- 'waiting', 'current', 'completed', 'cancelled'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    called_at DATETIME,
    completed_at DATETIME,
    FOREIGN KEY (client_id) REFERENCES users(id),
    FOREIGN KEY (room_id) REFERENCES queue_rooms(id)
);

-- Tabela mapowań lekarzy z plików CSV
CREATE TABLE IF NOT EXISTS csv_doctor_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    csv_doctor_name VARCHAR(200) NOT NULL,
    system_doctor_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES users(id),
    FOREIGN KEY (system_doctor_id) REFERENCES queue_doctors(id)
);

-- Tabela ustawień importu dla różnych systemów
CREATE TABLE IF NOT EXISTS import_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    system_name VARCHAR(100) NOT NULL, -- np. 'igabinet', 'medinet', etc.
    sync_code VARCHAR(16) UNIQUE NOT NULL, -- 16-znakowy kod synchronizacji
    is_active INTEGER DEFAULT 1,
    api_endpoint VARCHAR(255),
    api_credentials TEXT, -- JSON z danymi logowania
    last_sync DATETIME,
    sync_frequency INTEGER DEFAULT 3600, -- w sekundach
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES users(id),
    UNIQUE(client_id, system_name)
);

-- Tabela mapowań lekarzy z systemów zewnętrznych
CREATE TABLE IF NOT EXISTS external_doctor_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    import_setting_id INTEGER NOT NULL,
    external_doctor_id VARCHAR(100) NOT NULL,
    external_doctor_name VARCHAR(200) NOT NULL,
    external_doctor_specialization VARCHAR(200),
    system_doctor_id INTEGER,
    is_mapped INTEGER DEFAULT 0,
    last_seen DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (import_setting_id) REFERENCES import_settings(id),
    FOREIGN KEY (system_doctor_id) REFERENCES queue_doctors(id),
    UNIQUE(import_setting_id, external_doctor_id)
);

-- Tabela logów synchronizacji
CREATE TABLE IF NOT EXISTS sync_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    import_setting_id INTEGER NOT NULL,
    sync_type VARCHAR(50) NOT NULL, -- 'full', 'incremental', 'manual'
    status VARCHAR(20) NOT NULL, -- 'success', 'error', 'partial'
    records_processed INTEGER DEFAULT 0,
    records_updated INTEGER DEFAULT 0,
    records_created INTEGER DEFAULT 0,
    error_message TEXT,
    started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    FOREIGN KEY (import_setting_id) REFERENCES import_settings(id)
);
SQL;

// Hash hasła "password"
$password_hash = password_hash('password', PASSWORD_DEFAULT);

// Definicja przykładowych danych
$sample_data = <<<SQL
-- Użytkownicy (aktualne dane z bazy)
INSERT INTO users (id, username, email, password, role, company_name, balance, rate_per_second, is_active, created_at) VALUES
(1, 'admin', '<EMAIL>', '$2y$12$9HvdztpYFPhdNtNO516QpO1p1E18QUqxSuPXMgdkdOAB20DII.wAW', 'admin', 'Panel Administratora', 0, 0.0001, 1, '2025-08-27 06:39:01'),
(2, 'Sonokard', '<EMAIL>', '$2y$12$9HvdztpYFPhdNtNO516QpO1p1E18QUqxSuPXMgdkdOAB20DII.wAW', 'client', 'Sonokard', 1000.006, 0.0001, 1, '2025-08-27 06:39:01'),
(3, 'Polański', '<EMAIL>', '$2y$12$9HvdztpYFPhdNtNO516QpO1p1E18QUqxSuPXMgdkdOAB20DII.wAW', 'client', 'Piotr Polański', 1499.994, 0.0001, 1, '2025-08-27 06:39:01'),
(4, 'klient3', '<EMAIL>', '$2y$12$9HvdztpYFPhdNtNO516QpO1p1E18QUqxSuPXMgdkdOAB20DII.wAW', 'client', 'Trzecia Firma', 2000, 0.0001, 1, '2025-08-27 06:39:01');

-- Kategorie (aktualne dane z bazy)
INSERT INTO categories (id, name, description, created_at) VALUES
(1, 'Edukacja', 'Kampanie związane z edukacją', '2025-08-27 06:39:01'),
(2, 'Medycyna', 'Kampanie związane z medycyną i zdrowiem', '2025-08-27 06:39:01'),
(3, 'Firmy lokalne', 'Kampanie związane z lokalnymi firmami i usługami', '2025-08-27 06:39:01'),
(4, 'Finanse', 'Kampanie związane z finansami i bankowością', '2025-08-27 06:39:01'),
(5, 'Rozrywka', 'Kampanie związane z rozrywką i wydarzeniami', '2025-08-27 06:39:01'),
(6, 'Sport', 'Kampanie związane ze sportem i aktywnością fizyczną', '2025-08-27 06:39:01'),
(7, 'Żywność', 'Kampanie związane z żywnością i gastronomią', '2025-08-27 06:39:01'),
(8, 'Technologia', 'Kampanie związane z technologią i IT', '2025-08-27 06:39:01'),
(9, 'Moda', 'Kampanie związane z modą i ubraniami', '2025-08-27 06:39:01'),
(10, 'Motoryzacja', 'Kampanie związane z samochodami i pojazdami', '2025-08-27 06:39:01');

-- Przykładowe wyświetlacze klientów
INSERT INTO client_displays (client_id, display_name, display_code, is_online, last_heartbeat) VALUES
(2, 'Recepcja', 'abcdef', 0, NULL),
(2, 'Poczekalnia', 'ghijkl', 0, NULL),
(3, 'Sala główna', 'mnopqr', 0, NULL),
(4, 'Korytarz', 'stuvwx', 0, NULL);

-- Przykładowe kampanie
INSERT INTO campaigns (advertiser_id, name, description, media_type, media_url, youtube_id, duration, budget, max_frequency_per_hour, start_date, end_date, is_active, category_id) VALUES
(2, 'Wideo lokalne #1', 'Lokalne wideo reklamowe #1', 'video', '/uploads/campaigns/video-01.mp4', '', 30, 500, 10, date('now'), date('now', '+30 days'), 1, 5),
(3, 'Wideo lokalne #2', 'Lokalne wideo reklamowe #2', 'video', '/uploads/campaigns/video-02.mp4', '', 30, 300, 5, date('now'), date('now', '+30 days'), 1, 5),
(2, 'Wideo lokalne #3', 'Lokalne wideo reklamowe #3', 'video', '/uploads/campaigns/video-03.mp4', '', 30, 400, 8, date('now'), date('now', '+30 days'), 1, 5),
(3, 'Wideo lokalne #4', 'Lokalne wideo reklamowe #4', 'video', '/uploads/campaigns/video-04.mp4', '', 30, 350, 7, date('now'), date('now', '+30 days'), 1, 5),
(2, 'Wideo lokalne #5', 'Lokalne wideo reklamowe #5', 'video', '/uploads/campaigns/video-05.mp4', '', 30, 450, 9, date('now'), date('now', '+30 days'), 1, 5),
(3, 'Wideo lokalne #6', 'Lokalne wideo reklamowe #6', 'video', '/uploads/campaigns/video-06.mp4', '', 30, 380, 6, date('now'), date('now', '+30 days'), 1, 5),
(2, 'Wideo lokalne #7', 'Lokalne wideo reklamowe #7', 'video', '/uploads/campaigns/video-07.mp4', '', 30, 420, 8, date('now'), date('now', '+30 days'), 1, 5),
(3, 'Wideo lokalne #8', 'Lokalne wideo reklamowe #8', 'video', '/uploads/campaigns/video-08.mp4', '', 30, 360, 7, date('now'), date('now', '+30 days'), 1, 5),
(2, 'Wideo lokalne #9', 'Lokalne wideo reklamowe #9', 'video', '/uploads/campaigns/video-09.mp4', '', 30, 390, 8, date('now'), date('now', '+30 days'), 1, 5),
(3, 'Wideo lokalne #10', 'Lokalne wideo reklamowe #10', 'video', '/uploads/campaigns/video-10.mp4', '', 30, 410, 9, date('now'), date('now', '+30 days'), 1, 5),
(2, 'Wideo lokalne #11', 'Lokalne wideo reklamowe #11', 'video', '/uploads/campaigns/video-11.mp4', '', 30, 480, 10, date('now'), date('now', '+30 days'), 1, 5),
(3, 'Wideo lokalne #12', 'Lokalne wideo reklamowe #12', 'video', '/uploads/campaigns/video-12.mp4', '', 30, 440, 8, date('now'), date('now', '+30 days'), 1, 5),
(2, 'Wideo lokalne #13', 'Lokalne wideo reklamowe #13', 'video', '/uploads/campaigns/video-13.mp4', '', 30, 460, 9, date('now'), date('now', '+30 days'), 1, 5);

-- Przykładowe przypisania kampanii
INSERT INTO campaign_assignments (campaign_id, client_id, is_accepted) VALUES
(1, 3, 1),
(1, 4, 0),
(2, 2, 1),
(2, 4, 1),
(3, 3, 1),
(3, 4, 1),
(4, 2, 1),
(4, 3, 1),
(5, 2, 1),
(5, 4, 1),
(6, 2, 1),
(6, 3, 1),
(7, 3, 1),
(7, 4, 1),
(8, 2, 1),
(8, 3, 1),
(9, 2, 1),
(9, 4, 1),
(10, 3, 1),
(10, 4, 1),
(11, 2, 1),
(11, 3, 1),
(12, 2, 1),
(12, 4, 1),
(13, 3, 1),
(13, 4, 1);

-- Przykładowe wyświetlenia reklam (ostatnie 30 dni)
INSERT INTO ad_views (campaign_id, client_id, duration_seconds, cost, timestamp)
SELECT 
    CASE WHEN random() % 13 = 0 THEN 1 
         WHEN random() % 12 = 0 THEN 2 
         WHEN random() % 11 = 0 THEN 3 
         WHEN random() % 10 = 0 THEN 4 
         WHEN random() % 9 = 0 THEN 5 
         WHEN random() % 8 = 0 THEN 6 
         WHEN random() % 7 = 0 THEN 7 
         WHEN random() % 6 = 0 THEN 8 
         WHEN random() % 5 = 0 THEN 9 
         WHEN random() % 4 = 0 THEN 10 
         WHEN random() % 3 = 0 THEN 11 
         WHEN random() % 2 = 0 THEN 12 
         ELSE 13 END as campaign_id,
    CASE WHEN random() % 3 = 0 THEN 2 ELSE (CASE WHEN random() % 2 = 0 THEN 3 ELSE 4 END) END as client_id,
    (random() % 26) + 5 as duration_seconds,
    ((random() % 26) + 5) * 0.01 as cost,
    datetime('now', '-' || (random() % 30) || ' days', '-' || (random() % 24) || ' hours', '-' || (random() % 60) || ' minutes')
FROM (SELECT 1 FROM (WITH RECURSIVE cnt(x) AS (SELECT 1 UNION ALL SELECT x+1 FROM cnt LIMIT 50) SELECT x FROM cnt));

-- Przykładowe dane dla systemu kolejkowego
-- Włączenie systemu kolejkowego dla klientów
INSERT INTO queue_config (client_id, is_enabled) VALUES
(2, 1),
(3, 1),
(4, 1);

-- Lekarze (aktualne dane z bazy)
INSERT INTO queue_doctors (id, client_id, first_name, last_name, photo_url, specialization, access_code, active, created_at, default_room_id) VALUES
(1, 2, 'dr n. med. Małgorzata', 'Olesiak-Andryszczak', '/uploads/doctors/doctor_1_Olesiak-Andryszczak.jpg', 'Specjalista ginekologii i położnictwa, Specjalista perinatologii', 'amf8brwig2nw', 1, '2025-08-27 06:39:01', 1),
(2, 2, 'lek. Beata', 'Dawiec', '/uploads/doctors/doctor_2_Dawiec.jpg', 'Specjalista ginekologii i położnictwa', 'eagxc96nonfp', 1, '2025-08-27 06:39:01', 2),
(3, 2, 'lek. Natalia', 'Kubat', '/uploads/doctors/doctor_3_Kubat.jpg', 'Specjalista ginekologii i położnictwa, Specjalista perinatologii', 'dayj40aswk6o', 1, '2025-08-27 06:39:01', 5),
(4, 3, 'lek. Piotr', 'Polański', '/uploads/doctors/piotr.webp', 'Specjalista ginekologii i położnictwa', 'nhpsed4qn2nx', 1, '2025-08-27 06:39:01', NULL),
(5, 3, 'lek. Lucyna', 'Polańska', '/uploads/doctors/lucyna.webp', 'Specjalista ginekologii i położnictwa', 'f1ahqmz4smu9', 1, '2025-08-27 06:39:01', NULL),
(8, 2, 'dr n. med. Ewelina', 'Jasic-Szpak', '/uploads/doctors/doctor_8_Jasic-Szpak.jpg', 'Specjalista kardiologii', 'yhnrp3ejse59', 1, '2025-08-27 06:39:01', NULL),
(9, 2, 'dr n. med. Bożena', 'Dołęga-Kozierowska', '/uploads/doctors/doctor_9_Do____ga-Kozierowska.jpg', 'Specjalista alergologii, Specjalista pediatrii', 'x2volazwqitt', 1, '2025-08-27 06:39:01', 9),
(10, 2, 'dr n. k. f. Malwina', 'Pawik', '/uploads/doctors/doctor_10_Pawik.jpg', 'Doktor nauk o kulturze fizycznej, Magister fizjoterapii', 'gyl9rzm2a7h4', 1, '2025-08-27 06:39:01', 8),
(11, 2, 'lek. Przemysław', 'Piec', '/uploads/doctors/doctor_11_Piec.jpg', 'Specjalista ginekologii i położnictwa', '93s7rcxqkmiw', 1, '2025-08-27 06:39:01', 1),
(12, 2, 'dr n. med. Piotr', 'Siekanowicz', '/uploads/doctors/doctor_12_Siekanowicz.jpg', 'Specjalista chirurgii dziecięcej, Urolog dziecięcy', 'bob19i0simr1', 1, '2025-08-27 06:39:01', 6),
(13, 2, 'lek. Yuliia', 'Baraniak', '/uploads/doctors/doctor_13_Baraniak.jpg', 'Specjalista ginekologii i położnictwa', 'k9m2n4p8q6r3', 1, '2025-08-27 06:39:01', 1),
(15, 2, 'lek. Jakub', 'Andrzejewski', '/uploads/doctors/doctor_15_Andrzejewski.jpg', 'Specjalista ginekologii i położnictwa', 't7v8b9c1d2e4f', 1, '2025-08-27 06:39:01', NULL),
(16, 2, 'Pielęgniarka /', 'Położna', '/uploads/doctors/doctor_16_Po__o__na.png', 'Pielęgniarka, Położna', '4sn4c5kqthfn', 1, '2025-08-27 06:39:01', NULL),
(17, 2, 'lek. Ewelina', 'Sądaj', '/uploads/doctors/doctor_17_S__daj.jpg', 'Specjalista ginekologii i położnictwa', 'ouei0uehv61q', 1, '2025-08-27 09:50:39', NULL),
(18, 2, 'lek. Oliwia', 'Kopera', '/uploads/doctors/doctor_18_Kopera.jpg', 'Specjalista ginekologii i położnictwa', 'ap589m1ivu3k', 1, '2025-08-27 09:50:39', NULL),
(19, 2, 'dr n. med. Aneta', 'Walaszek-Gruszka', '/uploads/doctors/doctor_19_Walaszek-Gruszka.jpg', 'Specjalista ginekologii i położnictwa, Specjalista perinatologii', '7kv3k7tignf2', 1, '2025-08-27 09:50:39', NULL),
(20, 2, 'lek. Agnieszka', 'Tyszko-Tymińska', '/uploads/doctors/doctor_20_Tyszko-Tymi__ska.jpg', 'Specjalista ginekologii i położnictwa, Ginekolog Dziecięcy', '9truz9ve6ul6', 1, '2025-08-27 09:50:39', NULL),
(21, 2, 'lek. Joanna', 'Nestorowicz-Czernianin', '/uploads/doctors/doctor_21_Nestorowicz-Czernianin.jpg', 'Specjalista ginekologii i położnictwa', 'yp2wfnqq64og', 1, '2025-08-27 09:50:39', NULL),
(22, 2, 'lek. Tomasz', 'Kościelniak', '/uploads/doctors/doctor_22_Ko__cielniak.jpg', 'Specjalista ginekologii i położnictwa', 'c9mzmvi8kdrs', 1, '2025-08-27 09:50:39', NULL),
(23, 2, 'lek. Sylwia', 'Wnuk', '/uploads/doctors/doctor_23_Wnuk.jpg', 'Specjalista radiologii, Specjalista onkologii, Specjalista chorób wewnętrznych', '5fq64v35mbwc', 1, '2025-08-27 09:50:39', NULL),
(24, 2, 'mgr Aleksandra', 'Żurakowska', '/uploads/doctors/doctor_24___urakowska.jpg', 'Magister fizjoterapii', 'o6ecs603dpfa', 1, '2025-08-27 09:50:39', NULL),
(25, 2, 'dr n. med. Piotr', 'Miśkiewicz', '/uploads/doctors/doctor_25_Mi__kiewicz.jpg', 'Specjalista chirurgii dziecięcej, Specjalista ortopedii i traumatologii dziecięcej', 'a7xlfw0kje5e', 1, '2025-08-27 09:50:39', NULL),
(26, 2, 'dr n. med. Grzegorz', 'Dobaczewski', '/uploads/doctors/doctor_26_Dobaczewski.jpg', 'Specjalista onkologii i hematologii dziecięcej, Specjalista pediatrii', 'lgtignf7k8oa', 1, '2025-08-27 09:50:39', NULL),
(27, 2, 'dr n. med. Justyna', 'Kuliczkowska-Płaksej', '/uploads/doctors/doctor_27_Kuliczkowska-P__aksej.jpg', 'Specjalista endokrynologii, Specjalista chorób wewnętrznych', '30vkxmml9qpc', 1, '2025-08-27 09:50:39', NULL),
(28, 2, 'dr n. med. Barbara', 'Stachowska', '/uploads/doctors/doctor_28_Stachowska.jpg', 'Specjalista endokrynologii, Specjalista chorób wewnętrznych', '94g70d0xrhe3', 1, '2025-08-27 09:50:39', NULL),
(29, 2, 'dr n. med. Katarzyna', 'Kulej-Łyko', '/uploads/doctors/doctor_29_Kulej-__yko.jpg', 'Specjalista kardiologii', 'myo6yauarmbo', 1, '2025-08-27 09:50:39', NULL),
(30, 2, 'dr n. med. Marta', 'Obremska', '/uploads/doctors/doctor_30_Obremska.png', 'Specjalista kardiologii', 'ys959iyrlivh', 1, '2025-08-27 09:50:39', NULL),
(31, 2, 'dr n. med. Amelia', 'Głowaczewska-Wójcik', '/uploads/doctors/doctor_31_G__owaczewska-W__jcik.jpg', 'Specjalista dermatologii', 'q70xb0hvtptm', 1, '2025-08-27 09:50:39', NULL),
(32, 2, 'lek. Marta', 'Nogaj-Adamowicz', '/uploads/doctors/doctor_32_Nogaj-Adamowicz.jpg', 'Specjalista dermatologii', 'tpg6z8x7zqhr', 1, '2025-08-27 09:50:39', NULL),
(33, 2, 'dr n. med. Łukasz', 'Jabłoński', '/uploads/doctors/doctor_33_Jab__o__ski.jpg', 'Specjalista chirurgii, Specjalista chirurgii naczyniowej', 'd8cmzum6usvc', 1, '2025-08-27 09:50:39', NULL),
(34, 2, 'lek. Jerzy', 'Płochowski', '/uploads/doctors/doctor_34_P__ochowski.jpg', 'Specjalista medycyny sportowej, Specjalista ortopedii i traumatologii', 'dkc8hclou0gp', 1, '2025-08-27 09:50:39', NULL),
(35, 2, 'dr n. med. Ryszard', 'Ślęzak', '/uploads/doctors/doctor_35___l__zak.jpg', 'Specjalista genetyki klinicznej, Specjalista chorób wewnętrznych', 'mmb9rdudb1q2', 1, '2025-08-27 09:50:39', NULL),
(36, 2, 'Izabela', 'Śliwińska', '/uploads/doctors/doctor_36___liwi__ska.jpg', 'Magister położnictwa', 'suu0f1emklzi', 1, '2025-08-27 09:50:39', NULL),
(37, 2, 'Magdalena', 'Dudziec', '/uploads/doctors/doctor_37_Dudziec.jpg', 'Magister położnictwa', 'riye4vadlb0u', 1, '2025-08-27 09:50:39', NULL),
(38, 2, 'Klaudia', 'Bonar', '/uploads/doctors/doctor_38_Bonar.jpg', 'Położna', 'l0haqosrn4xw', 1, '2025-08-27 09:50:39', NULL);

-- Sale (aktualne dane z bazy)
INSERT INTO queue_rooms (id, client_id, name, description, doctor_id, room_number, active, created_at) VALUES
(1, 2, 'Gabinet 1', 'Gabinet kardiologiczny', 1, '1', 1, '2025-08-27 06:39:01'),
(2, 2, 'Gabinet 2', 'Gabinet neurologiczny', 2, '2', 1, '2025-08-27 06:39:01'),
(3, 2, 'Gabinet 3', 'Gabinet ortopedyczny', 3, '3', 1, '2025-08-27 06:39:01'),
(4, 3, 'Gabinet 4', 'Gabinet dermatologiczny', 4, '4', 1, '2025-08-27 06:39:01'),
(5, 3, 'Gabinet 5', 'Gabinet okulistyczny', 5, '5', 1, '2025-08-27 06:39:01'),
(6, 4, 'Gabinet 6', 'Gabinet pediatryczny', 6, '6', 1, '2025-08-27 06:39:01'),
(7, 4, 'Gabinet 7', 'Gabinet chirurgiczny', 7, '7', 1, '2025-08-27 06:39:01'),
(8, 2, 'Gabinet 5', '', 3, '4', 1, '2025-08-27 06:39:01'),
(9, 2, 'Gabinet 6', 'Gabinet ginekologiczny', 17, '6', 1, '2025-08-27 06:39:01'),
(10, 2, 'Gabinet 8', 'Gabinet położnej', 16, '8', 1, '2025-08-27 06:39:01'),
(11, 2, 'Gabinet 9', 'Gabinet ginekologiczny', 18, '9', 1, '2025-08-27 06:39:01'),
(12, 2, 'Gabinet 10', 'Gabinet specjalista ginekologii i położnictwa', 15, '10', 1, '2025-08-27 06:39:01');

-- Konfiguracja systemu kolejkowego dla klientów
INSERT INTO queue_config (client_id, is_enabled) VALUES
(2, 1),
(3, 1),
(4, 1);

-- Ustawienie domyślnych gabinetów dla wybranych lekarzy
UPDATE queue_doctors SET default_room_id = 1 WHERE id IN (1, 11, 13);
UPDATE queue_doctors SET default_room_id = 2 WHERE id = 2;
UPDATE queue_doctors SET default_room_id = 5 WHERE id IN (3, 14);
UPDATE queue_doctors SET default_room_id = 6 WHERE id = 12;
UPDATE queue_doctors SET default_room_id = 8 WHERE id = 10;
UPDATE queue_doctors SET default_room_id = 9 WHERE id = 9;

-- Ustawienia importu (aktualne dane z bazy)
INSERT INTO import_settings (id, client_id, system_name, sync_code, is_active, api_endpoint, last_sync, sync_frequency, created_at, updated_at) VALUES
(1, 2, 'igabinet', 'igab1234567890123', 1, 'https://sonokard.igabinet.pl/admin/request/work_schedule_request.php', '2025-08-29 07:21:21', 3600, '2025-08-27 06:39:01', '2025-08-27 06:39:01'),
(2, 3, 'igabinet', 'igab9876543210987', 1, 'https://sonokard.igabinet.pl/admin/request/work_schedule_request.php', NULL, 7200, '2025-08-27 06:39:01', '2025-08-27 06:39:01');

-- Mapowania lekarzy z systemu zewnętrznego (aktualne dane z bazy)
INSERT INTO external_doctor_mappings (id, import_setting_id, external_doctor_id, external_doctor_name, external_doctor_specialization, system_doctor_id, is_mapped, last_seen, created_at) VALUES
(1, 1, '114', 'ginekolog dr Ewelina Sądaj', 'Ginekolog', 17, 1, '2025-08-29 07:21:21', '2025-08-27 06:39:01'),
(2, 1, '129', 'ginekolog dr Jakub Andrzejewski', 'Ginekolog', 15, 1, '2025-08-29 07:21:21', '2025-08-27 06:39:01'),
(3, 1, '72', 'ginekolog dr Joanna Nestorowicz-Czernianin', 'Ginekolog', 21, 1, '2025-08-27T12:08:12Z', '2025-08-27 06:39:01'),
(4, 1, '20', 'ginekolog dr Natalia Kubat', 'Ginekolog', 3, 1, '2025-08-29 07:21:21', '2025-08-27 06:39:01'),
(5, 1, '1748423446', 'Gabinet Położnej', 'Kardiolog', 16, 1, '2025-08-29 07:21:21', '2025-08-27 06:39:01'),
(19, 1, '1755853152', 'dermatolog dr Amelia Głowaczewska-Wójcik', 'Dermatolog', 31, 1, '2025-08-29 07:21:21', '2025-08-27 11:52:54'),
(20, 1, '43', 'genetyk dr n.med. Ryszard Ślęzak', '', 35, 1, '2025-08-27T12:08:12Z', '2025-08-27 11:52:54'),
(21, 1, '105', 'ginekolog dr Beata Dawiec', 'Ginekolog', 2, 1, '2025-08-29 07:21:21', '2025-08-27 11:52:54'),
(22, 1, '10', 'ginekolog dr n.med. Małgorzata Olesiak-Andryszczak', 'Ginekolog', 1, 1, '2025-08-29 07:21:21', '2025-08-27 11:52:54'),
(23, 1, '83', 'ginekolog dr Oliwia Kopera', 'Ginekolog', 18, 1, '2025-08-29 07:21:21', '2025-08-27 11:52:54'),
(24, 1, '19', 'ginekolog dr Przemysław Piec', 'Ginekolog', 11, 1, '2025-08-27T12:08:12Z', '2025-08-27 11:52:54'),
(25, 1, '82', 'ginekolog dr Yuliia Baraniak', 'Ginekolog', 13, 1, '2025-08-27T12:08:12Z', '2025-08-27 11:52:54'),
(26, 1, '110', 'ginekolog dziecięcy dr Agnieszka Tyszko-Tymińska', 'Ginekolog', 20, 1, '2025-08-27T12:08:12Z', '2025-08-27 11:52:54'),
(27, 1, '1750949760', 'hepatolog, specjalista chorób zakaźnych dr Ewa Jarowicz', '', NULL, 0, '2025-08-27T12:08:12Z', '2025-08-27 11:52:54');
SQL;

// Inicjalizacja bazy danych
echo "Inicjalizacja bazy danych...\n";

// Upewnij się, że katalog database istnieje
if (!is_dir('database')) {
    mkdir('database', 0777, true);
    echo "Utworzono katalog database\n";
}

$db_file = 'database/reklama.db';

// Usuń istniejącą bazę danych
if (file_exists($db_file)) {
    unlink($db_file);
    echo "Usunięto istniejącą bazę danych\n";
}

try {
    // Utwórz nową bazę danych i połącz się z nią
    $db = new PDO('sqlite:' . $db_file);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Utworzono nową bazę danych\n";

    // Utwórz schemat bazy danych
    $db->exec($database_schema);
    echo "Utworzono schemat bazy danych\n";

    // Wstaw przykładowe dane
    $db->exec($sample_data);
    echo "Dodano przykładowe dane\n";

    echo "\nInicjalizacja bazy danych zakończona pomyślnie!\n";

    echo "\nDane logowania:\n";
    echo "Administrator: admin / password\n";
    echo "Klient 1: klient1 / password\n";
    echo "Klient 2: klient2 / password\n";
    echo "Klient 3: klient3 / password\n";
} catch (PDOException $e) {
    die("Błąd podczas inicjalizacji bazy danych: " . $e->getMessage() . "\n");
}

?>
// Lekarze Sonokard (zaktualizowane 2025-08-27 10:29:26)
$sonokardDoctors = [
[
'first_name' => 'dr n. med. Małgorzata',
'last_name' => 'Olesiak-Andryszczak',
'specialization' => '',
'photo_url' => '/uploads/doctors/doctor1.webp',
'access_code' => 'amf8brwig2nw'
],
[
'first_name' => 'lek. Beata',
'last_name' => 'Dawiec',
'specialization' => '',
'photo_url' => '/uploads/doctors/doctor2.webp',
'access_code' => 'eagxc96nonfp'
],
[
'first_name' => 'lek. Natalia',
'last_name' => 'Kubat',
'specialization' => '',
'photo_url' => '/uploads/doctors/doctor3.webp',
'access_code' => 'dayj40aswk6o'
],
[
'first_name' => 'dr n. med. Ewelina',
'last_name' => 'Jasic-Szpak',
'specialization' => '',
'photo_url' => '/uploads/doctors/684db9f624654.webp',
'access_code' => 'yhnrp3ejse59'
],
[
'first_name' => 'dr n. med. Bożena',
'last_name' => 'Dołęga-Kozierowska',
'specialization' => '',
'photo_url' => '/uploads/doctors/684edce2dcd27.webp',
'access_code' => 'x2volazwqitt'
],
[
'first_name' => 'dr n. k. f. Malwina',
'last_name' => 'Pawik',
'specialization' => '',
'photo_url' => '/uploads/doctors/684edd4e96b59.webp',
'access_code' => 'gyl9rzm2a7h4'
],
[
'first_name' => 'lek. Przemysław',
'last_name' => 'Piec',
'specialization' => '',
'photo_url' => '/uploads/doctors/684edd96d5765.webp',
'access_code' => '93s7rcxqkmiw'
],
[
'first_name' => 'dr n. med. Piotr',
'last_name' => 'Siekanowicz',
'specialization' => '',
'photo_url' => '/uploads/doctors/684eddc760b78.webp',
'access_code' => 'bob19i0simr1'
],
[
'first_name' => 'lek. Yuliia',
'last_name' => 'Baraniak',
'specialization' => '',
'photo_url' => '/uploads/doctors/684ede1c85843.webp',
'access_code' => 'k9m2n4p8q6r3'
],
[
'first_name' => 'lek. Julia',
'last_name' => 'Nestorowicz-Czernianin',
'specialization' => '',
'photo_url' => '/uploads/doctors/684ede6070b1c.webp',
'access_code' => 'w6q63ujhu6xt'
],
[
'first_name' => 'lek. Jakub',
'last_name' => 'Andrzejewski',
'specialization' => '',
'photo_url' => '/uploads/doctors/684ede86cf3a3.webp',
'access_code' => 't7v8b9c1d2e4f'
],
[
'first_name' => 'Pielęgniarka /',
'last_name' => 'Położna',
'specialization' => '',
'photo_url' => '',
'access_code' => '4sn4c5kqthfn'
],
[
'first_name' => 'lek. Ewelina',
'last_name' => 'Sądaj',
'specialization' => 'Specjalista ginekologii i położnictwa',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/2/a/csm_11160poziom_www_3864d45b53.jpg',
'access_code' => 'ouei0uehv61q'
],
[
'first_name' => 'lek. Oliwia',
'last_name' => 'Kopera',
'specialization' => 'Specjalista ginekologii i położnictwa',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/7/7/csm_162www_2090344a6a.jpg',
'access_code' => 'ap589m1ivu3k'
],
[
'first_name' => 'dr n. med. Aneta',
'last_name' => 'Walaszek-Gruszka',
'specialization' => 'Specjalista ginekologii i położnictwa, Specjalista perinatologii',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/a/d/csm_Dr_Aneta_Walaszek-Gruszka-png-327x490_ab8b9f8370.jpg',
'access_code' => '7kv3k7tignf2'
],
[
'first_name' => 'lek. Agnieszka',
'last_name' => 'Tyszko-Tymińska',
'specialization' => 'Specjalista ginekologii i położnictwa, Ginekolog Dziecięcy',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/6/2/csm_Tyszko_Tyminska_dfd54effd6.jpg',
'access_code' => '9truz9ve6ul6'
],
[
'first_name' => 'lek. Joanna',
'last_name' => 'Nestorowicz-Czernianin',
'specialization' => 'Specjalista ginekologii i położnictwa',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/1/c/csm_11286_poziom_f25c6505ad.jpg',
'access_code' => 'yp2wfnqq64og'
],
[
'first_name' => 'lek. Tomasz',
'last_name' => 'Kościelniak',
'specialization' => 'Specjalista ginekologii i położnictwa',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/4/d/csm_T_Koscielniak_black_a5a73ca48b.jpg',
'access_code' => 'c9mzmvi8kdrs'
],
[
'first_name' => 'lek. Sylwia',
'last_name' => 'Wnuk',
'specialization' => 'Specjalista radiologii, Specjalista onkologii, Specjalista chorób wewnętrznych',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/5/4/csm_11858www_64be17b062.jpg',
'access_code' => '5fq64v35mbwc'
],
[
'first_name' => 'mgr',
'last_name' => 'Aleksandra Żurakowska',
'specialization' => 'Magister fizjoterapii',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/e/8/csm_123www_db7b429856.jpg',
'access_code' => 'o6ecs603dpfa'
],
[
'first_name' => 'dr n. med. Piotr',
'last_name' => 'Miśkiewicz',
'specialization' => 'Specjalista chirurgii dziecięcej, Specjalista ortopedii i traumatologii dziecięcej',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/e/d/csm_076www_77a05dac4d.jpg',
'access_code' => 'a7xlfw0kje5e'
],
[
'first_name' => 'dr n. med. Grzegorz',
'last_name' => 'Dobaczewski',
'specialization' => 'Specjalista onkologii i hematologii dziecięcej, Specjalista pediatrii',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/9/9/csm_dobaczewski_01_062aac6763.jpg',
'access_code' => 'lgtignf7k8oa'
],
[
'first_name' => 'dr n. med. Justyna',
'last_name' => 'Kuliczkowska-Płaksej',
'specialization' => 'Specjalista endokrynologii, Specjalista chorób wewnętrznych',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/3/c/csm_kuliczkowska_black_ab5eeacb7e.jpg',
'access_code' => '30vkxmml9qpc'
],
[
'first_name' => 'dr n. med. Barbara',
'last_name' => 'Stachowska',
'specialization' => 'Specjalista endokrynologii, Specjalista chorób wewnętrznych',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/1/8/csm_11038_34d47de9f6.jpg',
'access_code' => '94g70d0xrhe3'
],
[
'first_name' => 'dr n. med. Katarzyna',
'last_name' => 'Kulej-Łyko',
'specialization' => 'Specjalista kardiologii',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/9/2/csm_Kulej_Lyko_327129096b.jpg',
'access_code' => 'myo6yauarmbo'
],
[
'first_name' => 'dr. n. med. Marta',
'last_name' => 'Obremska',
'specialization' => 'Specjalista kardiologii',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/c/4/csm_avatar_32d3b4fed0.png',
'access_code' => 'ys959iyrlivh'
],
[
'first_name' => 'dr n. med. Amelia',
'last_name' => 'Głowaczewska-Wójcik',
'specialization' => 'Specjalista dermatologii',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/2/f/csm_www_dr_Amelia_b052eac79e.jpg',
'access_code' => 'q70xb0hvtptm'
],
[
'first_name' => 'lek. Marta',
'last_name' => 'Nogaj-Adamowicz',
'specialization' => 'Specjalista dermatologii',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/5/9/csm_Marta_Nogaj_e12b985100.jpg',
'access_code' => 'tpg6z8x7zqhr'
],
[
'first_name' => 'dr n. med. Łukasz',
'last_name' => 'Jabłoński',
'specialization' => 'Specjalista chirurgii, Specjalista chirurgii naczyniowej',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/d/6/csm_Jablonski_ceaa0e7889.jpg',
'access_code' => 'd8cmzum6usvc'
],
[
'first_name' => 'lek. Jerzy',
'last_name' => 'Płochowski',
'specialization' => 'Specjalista medycyny sportowej, Specjalista ortopedii i traumatologii',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/f/f/csm_plochowski_83e7623dbe.jpg',
'access_code' => 'dkc8hclou0gp'
],
[
'first_name' => 'dr n. med. Ryszard',
'last_name' => 'Ślęzak',
'specialization' => 'Specjalista genetyki klinicznej, Specjalista chorób wewnętrznych',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/8/3/csm_ryszard_slezak_47f2e33716.jpg',
'access_code' => 'mmb9rdudb1q2'
],
[
'first_name' => '',
'last_name' => 'Izabela Śliwińska',
'specialization' => 'Magister położnictwa',
'photo_url' => '',
'access_code' => 'suu0f1emklzi'
],
[
'first_name' => '',
'last_name' => 'Magdalena Dudziec',
'specialization' => 'Magister położnictwa',
'photo_url' => '',
'access_code' => 'riye4vadlb0u'
],
[
'first_name' => '',
'last_name' => 'Klaudia Bonar',
'specialization' => 'Położna',
'photo_url' => '',
'access_code' => 'l0haqosrn4xw'
],
[
'first_name' => 'lek.&nbsp; Yuliia',
'last_name' => 'Baraniak',
'specialization' => 'Specjalista ginekologii i położnictwaSpecjalista perinatologii',
'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/8/e/csm_Yuliia_Baraniak-zaakceptowane_01_e32db26d86.jpg',
'access_code' => 'vt9famprlofw'
],
[
'first_name' => 'ul. Królewiecka 161/14,',
'last_name' => '54-117 Wrocław<br>
Budynek Blue Park, II piętro',
'specialization' => 'Specjalista endokrynologiiSpecjalista chorób wewnętrznych',
'photo_url' => '',
'access_code' => '6777t16mwbas'
],
[
'first_name' => 'tel. +48 577 655 977',
'last_name' => 'tel. +48 661 240 040<br>',
'specialization' => 'Specjalista endokrynologii Specjalista chorób wewnętrznych',
'photo_url' => '',
'access_code' => 'dlsg7ry8gvh8'
],
[
'first_name' => '<strong>Przychodnia</strong>',
'last_name' => 'Sonokard - Świdnica<br> (EZ-medica)',
'specialization' => 'Specjalista kardiologii',
'photo_url' => '',
'access_code' => '09ip4r7opqn9'
],
[
'first_name' => 'Władysława Sikorskiego 31,',
'last_name' => '58-100 Świdnica',
'specialization' => 'Specjalista kardiologii',
'photo_url' => '',
'access_code' => '5clekhx42nqm'
],
[
'first_name' => 'tel. +48 577 655 664
</p>
</div>

<div class=\"col-md-3\">
    <p><strong>Przychodnia</strong>',
        'last_name' => 'Sonokard - Mieroszów',
        'specialization' => 'Specjalista kardiologii',
        'photo_url' => '',
        'access_code' => 'eoaovejsavjm'
        ],
        [
        'first_name' => 'Nad Potokiem 4,',
        'last_name' => '58-350 Mieroszów',
        'specialization' => 'Specjalista dermatologii',
        'photo_url' => '',
        'access_code' => 'wks59v5x20ym'
        ],
        ];

        foreach ($sonokardDoctors as $doctor) {
        $stmt = $pdo->prepare("
        INSERT OR IGNORE INTO queue_doctors (client_id, first_name, last_name, specialization, photo_url, access_code, active, created_at)
        VALUES (2, ?, ?, ?, ?, ?, 1, datetime('now'))
        ");
        $stmt->execute([
        $doctor['first_name'],
        $doctor['last_name'],
        $doctor['specialization'],
        $doctor['photo_url'],
        $doctor['access_code']
        ]);
        }
        echo "Dodano lekarzy Sonokard\n";