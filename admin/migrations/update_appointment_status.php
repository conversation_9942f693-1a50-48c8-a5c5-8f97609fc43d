<?php

/**
 * Migracja: Aktualizacja statusów wizyt na nowy system
 * 
 * Zmienia statusy wizyt z:
 * - 'completed' -> 'closed'
 * - 'cancelled' -> 'closed'
 * 
 * Pozostawia:
 * - 'waiting' -> 'waiting'
 * - 'current' -> 'current'
 */

require_once __DIR__ . '/../core/Database.php';

try {
    $db = Database::getInstance()->getConnection();

    echo "🔄 Rozpoczynam migrację statusów wizyt...\n";

    // Sprawdź obecne statusy
    $stmt = $db->prepare("SELECT status, COUNT(*) as count FROM queue_appointments GROUP BY status");
    $stmt->execute();
    $currentStatuses = $stmt->fetchAll();

    echo "📊 Obecne statusy w bazie:\n";
    foreach ($currentStatuses as $status) {
        echo "   - {$status['status']}: {$status['count']} wizyt\n";
    }

    // Rozpocznij transakcję
    $db->beginTransaction();

    // <PERSON>mie<PERSON> 'completed' na 'closed'
    $stmt = $db->prepare("UPDATE queue_appointments SET status = 'closed' WHERE status = 'completed'");
    $completedUpdated = $stmt->execute();
    $completedCount = $stmt->rowCount();

    // Zmień 'cancelled' na 'closed'
    $stmt = $db->prepare("UPDATE queue_appointments SET status = 'closed' WHERE status = 'cancelled'");
    $cancelledUpdated = $stmt->execute();
    $cancelledCount = $stmt->rowCount();

    if ($completedUpdated && $cancelledUpdated) {
        $db->commit();
        echo "✅ Migracja zakończona pomyślnie!\n";
        echo "   - Zmieniono 'completed' -> 'closed': {$completedCount} wizyt\n";
        echo "   - Zmieniono 'cancelled' -> 'closed': {$cancelledCount} wizyt\n";

        // Sprawdź nowe statusy
        $stmt = $db->prepare("SELECT status, COUNT(*) as count FROM queue_appointments GROUP BY status");
        $stmt->execute();
        $newStatuses = $stmt->fetchAll();

        echo "📊 Nowe statusy w bazie:\n";
        foreach ($newStatuses as $status) {
            echo "   - {$status['status']}: {$status['count']} wizyt\n";
        }
    } else {
        $db->rollback();
        echo "❌ Błąd podczas migracji - wycofano zmiany\n";
        exit(1);
    }
} catch (Exception $e) {
    if (isset($db)) {
        $db->rollback();
    }
    echo "❌ Błąd migracji: " . $e->getMessage() . "\n";
    exit(1);
}

echo "🎉 Migracja zakończona!\n";
