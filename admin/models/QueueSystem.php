<?php

class QueueSystem {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    // Sprawdza czy system kolejkowy jest włączony dla danego klienta
    public function isEnabled($clientId) {
        $stmt = $this->db->prepare("
            SELECT is_enabled FROM queue_config 
            WHERE client_id = ?
        ");
        $stmt->execute([$clientId]);
        $result = $stmt->fetch();

        if (!$result) {
            return false;
        }

        return $result['is_enabled'] == 1;
    }

    // Włącza lub wyłącza system kolejkowy dla klienta
    public function toggleSystem($clientId, $isEnabled) {
        // Sprawdź czy konfiguracja już istnieje
        $stmt = $this->db->prepare("SELECT id FROM queue_config WHERE client_id = ?");
        $stmt->execute([$clientId]);
        $config = $stmt->fetch();

        if ($config) {
            // Aktualizuj istniejącą konfigurację
            $stmt = $this->db->prepare("
                UPDATE queue_config 
                SET is_enabled = ? 
                WHERE client_id = ?
            ");
            return $stmt->execute([$isEnabled ? 1 : 0, $clientId]);
        } else {
            // Utwórz nową konfigurację
            $stmt = $this->db->prepare("
                INSERT INTO queue_config (client_id, is_enabled)
                VALUES (?, ?)
            ");
            return $stmt->execute([$clientId, $isEnabled ? 1 : 0]);
        }
    }

    // Pobiera statystyki wizyt dla klienta
    public function getAppointmentStats($clientId, $date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }

        $stmt = $this->db->prepare("
            SELECT
                d.first_name || ' ' || d.last_name as doctor_name,
                d.specialization,
                r.name as room_name,
                COUNT(CASE WHEN a.status = 'waiting' THEN 1 END) as waiting_count,
                COUNT(CASE WHEN a.status = 'current' THEN 1 END) as current_count,
                COUNT(CASE WHEN a.status = 'closed' THEN 1 END) as completed_count
            FROM queue_doctors d
            JOIN queue_appointments a ON d.id = a.doctor_id AND a.appointment_date = ?
            LEFT JOIN queue_rooms r ON d.id = r.doctor_id AND r.client_id = ? AND r.active = 1
            WHERE a.client_id = ?
            GROUP BY d.id, d.first_name, d.last_name, d.specialization, r.name
            ORDER BY d.first_name, d.last_name
        ");
        $stmt->execute([$date, $clientId, $clientId]);
        return $stmt->fetchAll();
    }

    // Pobiera aktualną wizytę dla danego lekarza
    public function getCurrentAppointmentForDoctor($doctorId, $date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }

        $stmt = $this->db->prepare("
            SELECT * FROM queue_appointments
            WHERE doctor_id = ? AND appointment_date = ? AND status = 'current'
            ORDER BY started_at DESC
            LIMIT 1
        ");
        $stmt->execute([$doctorId, $date]);
        $result = $stmt->fetch();
        return $result;
    }

    // Pobiera listę oczekujących wizyt dla danego lekarza
    public function getWaitingAppointmentsForDoctor($doctorId, $limit = 5, $date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }

        $stmt = $this->db->prepare("
            SELECT * FROM queue_appointments
            WHERE doctor_id = ? AND appointment_date = ? AND status = 'waiting'
            ORDER BY appointment_time ASC
            LIMIT ?
        ");
        $stmt->execute([$doctorId, $date, $limit]);
        return $stmt->fetchAll();
    }



    // Pobiera statystyki dla lekarza
    public function getDoctorStats($doctorId, $date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }

        $stmt = $this->db->prepare("
            SELECT
                COUNT(CASE WHEN status = 'waiting' THEN 1 ELSE NULL END) as waiting_count,
                COUNT(CASE WHEN status = 'current' THEN 1 ELSE NULL END) as current_count,
                COUNT(CASE WHEN status = 'closed' THEN 1 ELSE NULL END) as completed_count,
                MAX(CASE WHEN status = 'current' THEN appointment_time ELSE NULL END) as current_time
            FROM queue_appointments
            WHERE doctor_id = ? AND appointment_date = ?
        ");
        $stmt->execute([$doctorId, $date]);
        return $stmt->fetch();
    }

    // Pobiera wszystkie wizyty dla danego lekarza (wszystkie statusy)
    public function getAllAppointmentsForDoctor($doctorId, $date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }

        $stmt = $this->db->prepare("
            SELECT qa.*, r.name as room_name, r.room_number
            FROM queue_appointments qa
            LEFT JOIN queue_rooms r ON qa.room_id = r.id
            WHERE qa.doctor_id = ? AND qa.appointment_date = ?
            ORDER BY qa.appointment_time ASC
        ");
        $stmt->execute([$doctorId, $date]);
        return $stmt->fetchAll();
    }

    // Wywołuje następną wizytę w kolejce dla lekarza
    public function callNextAppointmentForDoctor($doctorId, $date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }

        // Najpierw oznacz aktualną wizytę jako zakończoną
        $currentAppointment = $this->getCurrentAppointmentForDoctor($doctorId, $date);
        if ($currentAppointment) {
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'closed', completed_at = datetime('now')
                WHERE id = ?
            ");
            $stmt->execute([$currentAppointment['id']]);
        }

        // Wybierz najwcześniejszą oczekującą wizytę dla tego lekarza
        $stmt = $this->db->prepare("
            SELECT * FROM queue_appointments
            WHERE doctor_id = ? AND appointment_date = ? AND status = 'waiting'
            ORDER BY appointment_time ASC
            LIMIT 1
        ");
        $stmt->execute([$doctorId, $date]);
        $nextAppointment = $stmt->fetch();

        if (!$nextAppointment) {
            return false; // Brak kolejnych wizyt
        }

        // Ustaw tę wizytę jako aktualną
        $stmt = $this->db->prepare("
            UPDATE queue_appointments
            SET status = 'current', started_at = datetime('now')
            WHERE id = ?
        ");

        if ($stmt->execute([$nextAppointment['id']])) {
            return $nextAppointment;
        }

        return false;
    }

    // Pomija aktualną wizytę w kolejce dla lekarza
    public function skipCurrentAppointmentForDoctor($doctorId, $date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }

        $currentAppointment = $this->getCurrentAppointmentForDoctor($doctorId, $date);
        if (!$currentAppointment) {
            return false;
        }

        $stmt = $this->db->prepare("
            UPDATE queue_appointments
            SET status = 'waiting'
            WHERE id = ?
        ");

        if (!$stmt->execute([$currentAppointment['id']])) {
            return false;
        }

        return $this->callNextAppointmentForDoctor($doctorId, $date);
    }

    // Delegacja do modeli specyficznych
    public function getRooms($clientId) {
        return (new Room())->getByClientId($clientId);
    }

    public function getRoom($roomId, $clientId) {
        return (new Room())->getByIdAndClientId($roomId, $clientId);
    }

    public function addRoom($clientId, $name, $description = '', $doctorId = null, $roomNumber = '') {
        return (new Room())->create($clientId, $name, $description, $doctorId, $roomNumber);
    }

    public function updateRoom($roomId, $name, $description, $active, $doctorId = null, $roomNumber = '') {
        return (new Room())->update($roomId, $name, $description, $active, $doctorId, $roomNumber);
    }

    public function deleteRoom($roomId) {
        return (new Room())->delete($roomId);
    }

    public function getDoctors($clientId) {
        return (new Doctor())->getByClientId($clientId);
    }

    public function getDoctor($doctorId) {
        return (new Doctor())->getById($doctorId);
    }

    public function addDoctor($clientId, $firstName, $lastName, $specialization = '', $photoUrl = '', $active = true, $defaultRoomId = null) {
        return (new Doctor())->create($clientId, $firstName, $lastName, $specialization, $photoUrl, $active, $defaultRoomId);
    }

    public function updateDoctor($doctorId, $firstName, $lastName, $specialization, $photoUrl, $active, $defaultRoomId = null) {
        return (new Doctor())->update($doctorId, $firstName, $lastName, $specialization, $photoUrl, $active, $defaultRoomId);
    }

    public function deleteDoctor($doctorId) {
        return (new Doctor())->delete($doctorId);
    }

    public function getCurrentAppointment($roomId) {
        return (new Appointment())->getCurrentByRoomId($roomId);
    }

    public function getWaitingAppointments($roomId, $limit = 5, $date = null) {
        return (new Appointment())->getWaitingByRoomId($roomId, $limit, $date);
    }

    public function getAllAppointments($roomId, $date = null) {
        return (new Appointment())->getAllByRoomId($roomId, $date);
    }

    public function getAppointment($appointmentId) {
        return (new Appointment())->getById($appointmentId);
    }

    public function addAppointment($clientId, $roomId, $appointmentTime, $patientName = '', $appointmentDate = null, $doctorId = null) {
        return (new Appointment())->create($clientId, $roomId, $appointmentTime, $patientName, $appointmentDate, $doctorId);
    }

    public function updateAppointment($appointmentId, $appointmentTime, $patientName, $appointmentDate = null, $doctorId = null) {
        return (new Appointment())->update($appointmentId, $appointmentTime, $patientName, $appointmentDate, $doctorId);
    }

    public function deleteAppointment($appointmentId) {
        return (new Appointment())->delete($appointmentId);
    }

    public function callNextAppointment($roomId) {
        return (new Appointment())->callNext($roomId);
    }

    public function skipCurrentAppointment($roomId) {
        return (new Appointment())->skipCurrent($roomId);
    }
}
