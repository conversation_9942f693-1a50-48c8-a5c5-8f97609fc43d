<?php require_once "views/partials/header.php"; ?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Panel przychodni</h1>

    <!-- Sek<PERSON>ja systemu kolejkowego -->
    <section class="mb-5">
        <h2 class="section-title">
            <i class="fas fa-user-clock me-2"></i>
            System kolejkowy
        </h2>

        <!-- Karty statystyk kolejkowych -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary h-100">
                    <div class="card-body">
                        <div class="stats-card">
                            <div class="stats-icon primary">
                                <i class="fas fa-door-open"></i>
                            </div>
                            <div class="stats-info">
                                <h3><?= isset($queueStats)
                                        ? $queueStats["total_rooms"] ?? 0
                                        : 0 ?></h3>
                                <p>Gabinety</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success h-100">
                    <div class="card-body">
                        <div class="stats-card">
                            <div class="stats-icon success">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <div class="stats-info">
                                <h3><?= isset($queueStats)
                                        ? $queueStats["total_doctors"] ?? 0
                                        : 0 ?></h3>
                                <p>Lekarze</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info h-100">
                    <div class="card-body">
                        <div class="stats-card">
                            <div class="stats-icon info">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="stats-info">
                                <h3><?= isset($queueStats)
                                        ? $queueStats["today_appointments"] ?? 0
                                        : 0 ?></h3>
                                <p>Dzisiejsze wizyty</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning h-100">
                    <div class="card-body">
                        <div class="stats-card">
                            <div class="stats-icon warning">
                                <i class="fas fa-tv"></i>
                            </div>
                            <div class="stats-info">
                                <h3><?= isset($displays)
                                        ? count($displays)
                                        : 0 ?></h3>
                                <p>Wyświetlacze</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Panel szybkiego dostępu do systemu kolejkowego -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header d-flex justify-content-between align-items-center bg-primary text-white py-3">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-calendar-day me-2"></i>
                            Dzisiejsze wizyty
                        </h6>
                        <a href="<?= UrlHelper::url('client/queue') ?>" class="btn btn-light btn-sm">
                            <i class="fas fa-external-link-alt me-1"></i>
                            Zarządzaj wizytami
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if (
                            isset($queueStats) &&
                            ($queueStats["today_appointments"] ?? 0) > 0
                        ): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Godzina</th>
                                            <th>Gabinet</th>
                                            <th>Pacjent</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach (
                                            isset($todayAppointments)
                                                ? $todayAppointments
                                                : []
                                            as $appointment
                                        ): ?>
                                            <tr>
                                                <td><?= htmlspecialchars(
                                                        $appointment["appointment_time"],
                                                    ) ?></td>
                                                <td><?= htmlspecialchars(
                                                        $appointment["room_name"],
                                                    ) ?></td>
                                                <td><?= htmlspecialchars(
                                                        $appointment["patient_name"],
                                                    ) ?></td>
                                                <td>
                                                    <?php if (
                                                        $appointment["status"] == "waiting"
                                                    ): ?>
                                                        <span class="badge bg-warning text-dark">Oczekuje</span>
                                                    <?php elseif (
                                                        $appointment["status"] == "serving"
                                                    ): ?>
                                                        <span class="badge bg-primary">W trakcie</span>
                                                    <?php elseif (
                                                        $appointment["status"] == "completed"
                                                    ): ?>
                                                        <span class="badge bg-success">Zakończona</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary"><?= ucfirst(
                                                                                                $appointment["status"],
                                                                                            ) ?></span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-3 text-center">
                                <a href="<?= UrlHelper::url('client/queue') ?>" class="btn btn-primary">
                                    <i class="fas fa-user-clock me-1"></i> Przejdź do systemu kolejkowego
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <div class="display-4 text-muted mb-3">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <h5 class="text-muted mb-3">Brak wizyt na dzisiaj</h5>
                                <p class="text-muted mb-4">Dodaj wizyty, aby rozpocząć zarządzanie kolejką pacjentów</p>
                                <a href="<?= UrlHelper::url('client/queue') ?>" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i> Dodaj wizytę
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header d-flex justify-content-between align-items-center bg-success text-white py-3">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-door-open me-2"></i>
                            Twoje gabinety
                        </h6>
                        <div>
                            <a href="<?= UrlHelper::url('client/queue/rooms') ?>" class="btn btn-light btn-sm me-1">
                                <i class="fas fa-cog me-1"></i>
                                Zarządzaj
                            </a>
                            <a href="<?= UrlHelper::url('client/queue/rooms/create') ?>" class="btn btn-light btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                Dodaj
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (
                            isset($queueStats) &&
                            ($queueStats["total_rooms"] ?? 0) > 0
                        ): ?>
                            <div class="row">
                                <?php foreach (
                                    isset($rooms) ? $rooms : []
                                    as $room
                                ): ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="card border-0 shadow-sm h-100">
                                            <div class="card-body">
                                                <h6 class="card-title">
                                                    <i class="fas fa-door-open me-1 text-success"></i>
                                                    <?= htmlspecialchars(
                                                        $room["name"],
                                                    ) ?>
                                                </h6>
                                                <p class="card-text small text-muted">
                                                    <?= htmlspecialchars(
                                                        $room["description"] ?:
                                                            "Brak opisu",
                                                    ) ?>
                                                </p>
                                                <div class="mt-auto">
                                                    <a href="<?= UrlHelper::url('client/queue/doctor/' . $room['id']) ?>" class="btn btn-sm btn-outline-primary">
                                                        Panel lekarza
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="card-footer bg-transparent small text-muted">
                                                <?php if ($room["active"]): ?>
                                                    <span class="text-success">
                                                        <i class="fas fa-check-circle"></i> Aktywny
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-danger">
                                                        <i class="fas fa-times-circle"></i> Nieaktywny
                                                    </span>
                                                <?php endif; ?>
                                                <?php if (
                                                    $room["doctor_name"]
                                                ): ?>
                                                    <span class="float-end">
                                                        <i class="fas fa-user-md"></i> <?= htmlspecialchars(
                                                                                            $room["doctor_name"],
                                                                                        ) ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <div class="display-4 text-muted mb-3">
                                    <i class="fas fa-door-closed"></i>
                                </div>
                                <h5 class="text-muted mb-3">Brak dodanych gabinetów</h5>
                                <p class="text-muted mb-4">Dodaj gabinety, aby rozpocząć zarządzanie kolejką pacjentów</p>
                                <a href="<?= UrlHelper::url('client/queue/rooms/create') ?>" class="btn btn-success">
                                    <i class="fas fa-plus me-1"></i> Dodaj gabinet
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Sekcja wyświetlaczy i reklam -->
    <section>
        <h2 class="section-title">
            <i class="fas fa-tv me-2"></i>
            Wyświetlacze i reklamy
        </h2>

        <div class="row">
            <!-- Wyświetlacze -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header d-flex justify-content-between align-items-center bg-info text-white py-3">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-tv me-2"></i>
                            Twoje wyświetlacze
                        </h6>
                        <div>
                            <a href="<?= UrlHelper::url('client/displays') ?>" class="btn btn-light btn-sm me-1">
                                <i class="fas fa-cog me-1"></i>
                                Zarządzaj
                            </a>
                            <?php if (isset($displays) && count($displays) > 0): ?>
                                <a href="/wyswietlacz/<?= $displays[0]['display_code'] ?>" class="btn btn-light btn-sm" target="_blank">
                                    <i class="fas fa-desktop me-1"></i>
                                    Podgląd wyświetlacza
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (isset($displays) && count($displays) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Nazwa</th>
                                            <th>Status</th>
                                            <th>Ostatnia aktywność</th>
                                            <th>Akcje</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach (
                                            isset($displays) ? $displays : []
                                            as $display
                                        ): ?>
                                            <tr>
                                                <td><?= htmlspecialchars(
                                                        $display["display_name"],
                                                    ) ?></td>
                                                <td>
                                                    <?php if (
                                                        $display["is_online"]
                                                    ): ?>
                                                        <span class="badge bg-success">Online</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Offline</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?= $display["last_heartbeat"]
                                                        ? date(
                                                            "d.m.Y H:i:s",
                                                            strtotime(
                                                                $display["last_heartbeat"],
                                                            ),
                                                        )
                                                        : "Nigdy" ?>
                                                </td>
                                                <td>
                                                    <a href="/wyswietlacz/<?= $display['display_code'] ?>" class="btn btn-sm btn-primary" target="_blank">
                                                        <i class="fas fa-tv"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <div class="display-4 text-muted mb-3">
                                    <i class="fas fa-tv"></i>
                                </div>
                                <h5 class="text-muted mb-3">Brak wyświetlaczy</h5>
                                <p class="text-muted mb-4">Dodaj wyświetlacz, aby pokazać system kolejkowy w poczekalni</p>
                                <a href="<?= UrlHelper::url('client/displays') ?>" class="btn btn-info">
                                    <i class="fas fa-plus me-1"></i> Dodaj wyświetlacz
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Reklamy -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header d-flex justify-content-between align-items-center bg-secondary text-white py-3">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-ad me-2"></i>
                            Reklamy (opcjonalnie)
                        </h6>
                        <a href="<?= UrlHelper::url('client/available-campaigns') ?>" class="btn btn-light btn-sm">
                            <i class="fas fa-list me-1"></i>
                            Przeglądaj dostępne
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info mb-4">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="fas fa-info-circle fa-2x"></i>
                                </div>
                                <div>
                                    <h6 class="alert-heading">Informacja o reklamach</h6>
                                    <p class="mb-0">System reklamowy jest dodatkiem do systemu kolejkowego. Możesz wyświetlać reklamy pomiędzy informacjami o kolejkach, ale nie jest to wymagane.</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-light h-100">
                                    <div class="card-body text-center py-3">
                                        <h6 class="text-muted mb-2">Dostępne kampanie</h6>
                                        <h3 class="display-4 fw-bold"><?= isset(
                                                                            $adStats,
                                                                        )
                                                                            ? $adStats["available_campaigns"] ??
                                                                            0
                                                                            : 0 ?></h3>
                                        <a href="<?= UrlHelper::url('client/available-campaigns') ?>" class="btn btn-sm btn-outline-secondary mt-2">
                                            <i class="fas fa-search me-1"></i> Przeglądaj
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light h-100">
                                    <div class="card-body text-center py-3">
                                        <h6 class="text-muted mb-2">Twoje saldo</h6>
                                        <h3 class="display-4 fw-bold"><?= isset(
                                                                            $user,
                                                                        )
                                                                            ? number_format(
                                                                                $user["balance"] ?? 0,
                                                                                2,
                                                                            )
                                                                            : "0.00" ?> zł</h3>
                                        <small class="text-muted">z wyświetlania reklam</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<?php require_once "views/partials/footer.php"; ?>