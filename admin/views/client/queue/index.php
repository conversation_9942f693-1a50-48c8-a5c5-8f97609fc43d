<?php require_once 'views/partials/header.php'; ?>

<style>
    .doctor-card {
        transition: opacity 0.3s ease, transform 0.3s ease;
    }

    .doctor-card[style*="display: none"] {
        opacity: 0;
        transform: scale(0.95);
    }

    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .form-check-label {
        font-weight: 500;
        color: #495057;
    }

    #emptyDoctorsMessage {
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

<div class="bg-primary text-white py-3 mb-4">
    <div class="container-fluid d-flex justify-content-between align-items-center">
        <h4 class="mb-0 d-flex align-items-center">
            <i class="fas fa-user-md me-2"></i> Akt<PERSON><PERSON> k<PERSON>a
        </h4>
        <div class="d-flex gap-2">
            <a href="<?= UrlHelper::url('client/queue/doctors') ?>" class="btn btn-light text-primary" title="Zarządzaj lekarzami">
                <i class="fas fa-user-md"></i> Lekarze
            </a>
            <a href="<?= UrlHelper::url('client/queue/rooms') ?>" class="btn btn-light text-primary" title="Zarządzaj gabinetami">
                <i class="fas fa-door-open"></i> Gabinety
            </a>
            <a href="<?= UrlHelper::url('client/queue/import-csv') ?>" class="btn btn-light text-primary" title="Import danych z CSV">
                <i class="fas fa-file-csv"></i> Import CSV
            </a>
            <form method="GET" action="<?= UrlHelper::url('client/queue') ?>" class="d-flex align-items-end gap-2 mb-0 ms-3">
                <button type="submit" name="date" value="<?= date('Y-m-d', strtotime($selectedDate . ' -1 day')) ?>" class="btn btn-outline-light">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <input type="date" class="form-control" id="selected_date" value="<?= htmlspecialchars($selectedDate) ?>" onchange="submitDate(this.value)" style="width: 160px;">
                <button type="submit" name="date" value="<?= date('Y-m-d', strtotime($selectedDate . ' +1 day')) ?>" class="btn btn-outline-light">
                    <i class="fas fa-chevron-right"></i>
                </button>
                <a href="<?= UrlHelper::url('client/queue') ?>" class="btn btn-outline-light" title="Dzisiaj">
                    <i class="fas fa-calendar-day"></i>
                </a>
            </form>
        </div>
    </div>
</div>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-12">
            <?php if (isset($_SESSION['flash_message'])): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle"></i> <?= $_SESSION['flash_message'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    <?php unset($_SESSION['flash_message']); ?>
                </div>
            <?php endif; ?>

            <?php if ($isEnabled): ?>
                <!-- Lista lekarzy z kolejkami -->
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4">
                        <?php if (empty($doctors)): ?>
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-user-md fa-4x mb-3"></i>
                                <h4>Brak lekarzy</h4>
                                <p>Dodaj pierwszego lekarza, aby rozpocząć zarządzanie kolejkami.</p>
                                <a href="<?= UrlHelper::url('client/queue/doctors/create') ?>" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Dodaj lekarza
                                </a>
                            </div>
                        <?php else: ?>
                            <!-- Przycisk ukrywania lekarzy bez wizyt -->
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">
                                    Lekarze na dzień <?= date('d.m.Y', strtotime($selectedDate)) ?>
                                    <span class="badge bg-secondary ms-2" id="doctorsCount"><?= count($doctors) ?></span>
                                </h5>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="hideEmptyDoctors" onchange="toggleEmptyDoctors()">
                                    <label class="form-check-label" for="hideEmptyDoctors">
                                        <i class="fas fa-eye-slash me-1"></i>
                                        Ukryj lekarzy bez wizyt
                                    </label>
                                </div>
                            </div>

                            <div class="row" id="doctorsContainer">
                                <?php foreach ($doctors as $doctor): ?>
                                    <?php
                                    // Pobierz statystyki dla tego lekarza
                                    $doctorStats = $queueSystem->getDoctorStats($doctor['id'], $selectedDate);
                                    $currentAppointment = $queueSystem->getCurrentAppointmentForDoctor($doctor['id'], $selectedDate);
                                    $allAppointments = $queueSystem->getAllAppointmentsForDoctor($doctor['id'], $selectedDate);
                                    $waitingAppointments = array_filter($allAppointments, function ($apt) {
                                        return $apt['status'] === 'waiting';
                                    });

                                    // Sprawdź czy lekarz ma wizyty dzisiaj
                                    $hasAppointments = $currentAppointment || !empty($waitingAppointments) || ($doctorStats['waiting_count'] > 0) || ($doctorStats['completed_count'] > 0);
                                    $emptyClass = $hasAppointments ? '' : 'doctor-empty';
                                    ?>
                                    <div class="col-md-6 col-lg-4 mb-4 doctor-card <?= $emptyClass ?>" data-doctor-id="<?= $doctor['id'] ?>" data-has-appointments="<?= $hasAppointments ? 'true' : 'false' ?>">
                                        <div class="card h-100 border-0 shadow-sm">
                                            <div class="card-header bg-primary text-white">
                                                <div class="d-flex justify-content-between align-items-center w-100">
                                                    <div class="d-flex align-items-center">
                                                        <?php if ($doctor['photo_url']): ?>
                                                            <img src="<?= htmlspecialchars($doctor['photo_url']) ?>"
                                                                alt="Zdjęcie lekarza"
                                                                style="width: 60px; height: 60px; border-radius: 50%; object-fit: cover; margin-right: 15px;">
                                                        <?php else: ?>
                                                            <div style="width: 60px; height: 60px; border-radius: 50%; background-color: rgba(255,255,255,0.2); display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                                                                <i class="fas fa-user-md fa-2x"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                        <div>
                                                            <h6 class="mb-0" style="font-size: 1.1rem; color: #fff;">
                                                                <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                                            </h6>
                                                            <?php if ($doctor['specialization']): ?>
                                                                <small class="text-white-50"><?= htmlspecialchars($doctor['specialization']) ?></small>
                                                            <?php endif; ?>
                                                            <?php if (isset($doctor['default_room_name']) && !empty($doctor['default_room_name'])): ?>
                                                                <div class="mt-1">
                                                                    <span style="font-size: 0.95rem; color: #e0e0e0; font-weight: 500;">
                                                                        <?= htmlspecialchars($doctor['default_room_name']) ?>
                                                                    </span>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <a href="<?= UrlHelper::url('client/queue/doctor-queue/' . $doctor['id'] . '#addAppointmentModal') ?>" class="btn btn-light" title="Dodaj wizytę">
                                                        <i class="fas fa-plus"></i>
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <!-- Aktualna wizyta -->
                                                <h6 class="text-muted mb-2"><i class="fas fa-play-circle"></i> Aktualna wizyta</h6>
                                                <?php if ($currentAppointment): ?>
                                                    <div class="alert alert-info py-2 mb-3 d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <div class="fw-bold fs-5"><?= htmlspecialchars($currentAppointment['appointment_time']) ?></div>
                                                            <small class="text-muted"><?= htmlspecialchars($currentAppointment['patient_name'] ?: 'Pacjent') ?></small>
                                                        </div>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-success" onclick="completeAppointment(<?= $doctor['id'] ?>)">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                            <button class="btn btn-warning" onclick="skipAppointment(<?= $doctor['id'] ?>)">
                                                                <i class="fas fa-forward"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="alert alert-light py-2 mb-3 text-center text-muted">
                                                        <i class="fas fa-clock"></i> Brak aktualnej wizyty
                                                    </div>
                                                <?php endif; ?>

                                                <!-- Wszystkie wizyty -->
                                                <h6 class="text-muted mb-2"><i class="fas fa-list"></i> Wizyty (<?= count($allAppointments) ?>)</h6>
                                                <?php if (empty($allAppointments)): ?>
                                                    <div class="text-muted text-center py-2 mb-3">
                                                        <small>Brak wizyt</small>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="mb-3 appointment-scroll pe-2" style="max-height: 300px; overflow-y: auto; scrollbar-width: thin; scrollbar-color: #dee2e6 #f8f9fa;">
                                                        <?php foreach ($allAppointments as $appointment): ?>
                                                            <?php
                                                            $statusClass = '';
                                                            $statusIcon = '';
                                                            $statusText = '';

                                                            if ($appointment['status'] === 'current') {
                                                                $statusClass = 'bg-primary text-white';
                                                                $statusIcon = 'fas fa-play';
                                                            } elseif ($appointment['status'] === 'closed') {
                                                                $statusClass = 'bg-light text-muted';
                                                                $statusIcon = 'fas fa-check';
                                                            } else {
                                                                $statusClass = '';
                                                                $statusIcon = 'fas fa-clock';
                                                            }
                                                            ?>
                                                            <div class="d-flex justify-content-between align-items-center py-1 border-bottom appointment-item <?= $statusClass ?>" style="border-radius: 4px; margin-bottom: 2px; padding: 8px !important;">
                                                                <div class="flex-grow-1">
                                                                    <div class="d-flex align-items-center">
                                                                        <i class="<?= $statusIcon ?> me-2" style="min-width: 16px;"></i>
                                                                        <span class="fw-bold me-2" style="min-width: 50px;"><?= htmlspecialchars($appointment['appointment_time']) ?></span>
                                                                        <span><?= htmlspecialchars($appointment['patient_name'] ?: 'Pacjent') ?></span>
                                                                    </div>
                                                                </div>
                                                                <?php if ($appointment['status'] === 'waiting'): ?>
                                                                    <div class="d-flex align-items-center gap-1 ms-2">
                                                                        <button class="btn btn-primary btn-sm btn-compact" onclick="callNext(<?= $doctor['id'] ?>)" title="Wywołaj">
                                                                            <i class="fas fa-play"></i>
                                                                        </button>
                                                                        <a href="<?= UrlHelper::url('client/queue/appointments/edit/' . $appointment['id']) ?>" class="btn btn-outline-secondary btn-sm btn-compact" title="Edytuj wizytę">
                                                                            <i class="fas fa-edit"></i>
                                                                        </a>
                                                                        <a href="<?= UrlHelper::url('client/queue/appointments/delete/' . $appointment['id']) ?>" class="btn btn-outline-danger btn-sm btn-compact" title="Usuń wizytę" onclick="return confirm('Czy na pewno chcesz usunąć tę wizytę?')">
                                                                            <i class="fas fa-trash"></i>
                                                                        </a>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                <?php endif; ?>

                                                <!-- Statystyki -->
                                                <div class="d-flex justify-content-around mt-4 pt-3 border-top">
                                                    <div class="text-center">
                                                        <div class="fw-bold" style="color: green;"><?= $doctorStats['completed_count'] ?? 0 ?></div>
                                                        <small class="text-muted">Zakończone</small>
                                                    </div>
                                                    <div class="text-center">
                                                        <div class="fw-bold" style="color: orange;"><?= $doctorStats['waiting_count'] ?? 0 ?></div>
                                                        <small class="text-muted">Oczekujące</small>
                                                    </div>
                                                    <div class="text-center">
                                                        <div class="fw-bold" style="color: red;"><?= $doctorStats['cancelled_count'] ?? 0 ?></div>
                                                        <small class="text-muted">Anulowane</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>System kolejkowy jest wyłączony.</strong> Włącz go, aby móc zarządzać kolejkami lekarzy.
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i> O systemie kolejkowym
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>System kolejkowy pozwala pracownikom rejestracji na zarządzanie wizytami i kolejkami lekarzy w przychodni.</p>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Główne funkcje:</h6>
                                <ul>
                                    <li>Zarządzanie kolejkami lekarzy</li>
                                    <li>Wywoływanie pacjentów</li>
                                    <li>Monitorowanie statusu wizyt</li>
                                    <li>Statystyki dzienne</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Korzyści:</h6>
                                <ul>
                                    <li>Lepsze zarządzanie czasem</li>
                                    <li>Redukcja kolejek</li>
                                    <li>Przejrzysty system</li>
                                    <li>Łatwe monitorowanie</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

        </div>
    </div>
</div>

<style>
    /* Style dla przewijania w kartach lekarzy */
    .appointment-scroll::-webkit-scrollbar {
        width: 6px;
    }

    .appointment-scroll::-webkit-scrollbar-track {
        background: #f8f9fa;
        border-radius: 3px;
    }

    .appointment-scroll::-webkit-scrollbar-thumb {
        background: #dee2e6;
        border-radius: 3px;
    }

    .appointment-scroll::-webkit-scrollbar-thumb:hover {
        background: #adb5bd;
    }

    /* Efekt hover dla wizyt */
    .appointment-item {
        transition: background-color 0.2s ease;
    }

    .appointment-item:hover {
        background-color: #f8f9fa;
    }

    /* Kompaktowe przyciski */
    .btn-compact {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        line-height: 1.2;
    }

    /* Animacja dla przycisku dodawania */
    .card-header .btn-light {
        transition: transform 0.2s ease, background-color 0.2s ease;
        margin-left: 10px;
    }

    .card-header .btn-light:hover {
        transform: scale(1.1);
        background-color: #e9ecef;
    }

    /* Odstęp dla paska przewijania */
    .appointment-scroll {
        padding-right: 5px;
    }
</style>

<script>
    const selectedDate = '<?= $_GET['date'] ?? date('Y-m-d') ?>';

    function submitDate(dateValue) {
        if (dateValue) {
            window.location.href = `/admin/client/queue?date=${dateValue}`;
        }
    }

    function callNext(doctorId) {
        fetch(`/admin/client/queue/callNextAppointmentForDoctor/${doctorId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `date=${selectedDate}`
            })
            .then(response => {
                if (response.redirected) {
                    window.location.href = response.url;
                    return;
                }
                return response.json();
            })
            .then(data => {
                if (data && data.success) {
                    location.reload();
                } else if (data) {
                    alert(data.message || 'Wystąpił błąd');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Wystąpił błąd podczas wywoływania następnej wizyty');
            });
    }

    function completeAppointment(doctorId) {
        callNext(doctorId); // Zakończenie aktualnej wizyty i wywołanie następnej
    }

    function skipAppointment(doctorId) {
        fetch(`/admin/client/queue/skipCurrentAppointmentForDoctor/${doctorId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `date=${selectedDate}`
            })
            .then(response => {
                if (response.redirected) {
                    window.location.href = response.url;
                    return;
                }
                return response.json();
            })
            .then(data => {
                if (data && data.success) {
                    location.reload();
                } else if (data) {
                    alert(data.message || 'Wystąpił błąd');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Wystąpił błąd podczas pomijania wizyty');
            });
    }

    function callNextForAll() {
        if (confirm('Czy na pewno chcesz wywołać następną wizytę dla wszystkich lekarzy?')) {
            const doctorCards = document.querySelectorAll('.card[data-doctor-id]'); // Select cards by data attribute
            let completed = 0;

            doctorCards.forEach(card => {
                const doctorId = card.getAttribute('data-doctor-id');
                if (doctorId) {
                    callNext(doctorId);
                    completed++;
                }
            });

            if (completed === 0) {
                alert('Nie znaleziono lekarzy do wywołania');
            }
        }
    }

    // Funkcja ukrywania/pokazywania lekarzy bez wizyt
    function toggleEmptyDoctors() {
        const checkbox = document.getElementById('hideEmptyDoctors');
        const doctorCards = document.querySelectorAll('.doctor-card');
        let visibleCount = 0;

        doctorCards.forEach(card => {
            const hasAppointments = card.getAttribute('data-has-appointments') === 'true';

            if (checkbox.checked && !hasAppointments) {
                // Ukryj lekarzy bez wizyt
                card.style.display = 'none';
            } else {
                // Pokaż wszystkich lekarzy
                card.style.display = 'block';
                visibleCount++;
            }
        });

        // Aktualizuj licznik lekarzy
        const doctorsCount = document.getElementById('doctorsCount');
        if (doctorsCount) {
            doctorsCount.textContent = checkbox.checked ? visibleCount : doctorCards.length;
        }

        // Zapisz preferencję w localStorage
        localStorage.setItem('hideEmptyDoctors', checkbox.checked);

        // Pokaż komunikat o liczbie ukrytych lekarzy
        updateEmptyDoctorsMessage();
    }

    // Funkcja aktualizująca komunikat o ukrytych lekarzach
    function updateEmptyDoctorsMessage() {
        const checkbox = document.getElementById('hideEmptyDoctors');
        const doctorCards = document.querySelectorAll('.doctor-card');
        const emptyDoctors = document.querySelectorAll('.doctor-card[data-has-appointments="false"]');

        // Usuń poprzedni komunikat
        const existingMessage = document.getElementById('emptyDoctorsMessage');
        if (existingMessage) {
            existingMessage.remove();
        }

        if (checkbox.checked && emptyDoctors.length > 0) {
            const message = document.createElement('div');
            message.id = 'emptyDoctorsMessage';
            message.className = 'alert alert-info mt-2 mb-3';
            message.innerHTML = `
                <i class="fas fa-info-circle"></i>
                Ukryto ${emptyDoctors.length} lekarzy bez wizyt na dzień ${new Date('<?= $selectedDate ?>').toLocaleDateString('pl-PL')}
            `;

            const doctorsContainer = document.getElementById('doctorsContainer');
            doctorsContainer.parentNode.insertBefore(message, doctorsContainer);
        }
    }

    // Przywróć preferencję z localStorage przy ładowaniu strony
    document.addEventListener('DOMContentLoaded', function() {
        const hideEmptyDoctors = localStorage.getItem('hideEmptyDoctors') === 'true';
        const checkbox = document.getElementById('hideEmptyDoctors');

        if (checkbox && hideEmptyDoctors) {
            checkbox.checked = true;
            toggleEmptyDoctors();
        }
    });

    // Auto-refresh co 30 sekund
    setInterval(() => {
        // Sprawdź czy użytkownik jest aktywny
        if (!document.hidden) {
            location.reload();
        }
    }, 30000);
</script>

<?php require_once 'views/partials/footer.php'; ?>