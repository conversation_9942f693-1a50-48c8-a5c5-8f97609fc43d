<?php

class DisplayApiController extends ApiController {

    /**
     * <PERSON>bierz dane dla wyświetlacza
     */
    public function getDisplayData($code) {
        try {
            // Znajdź wyświetlacz po kodzie
            $stmt = $this->db->prepare("
                SELECT d.id, d.client_id, d.display_name as name, d.display_code,
                       d.is_online, d.last_heartbeat, d.created_at,
                       u.company_name, qc.is_enabled as queue_enabled
                FROM client_displays d
                JOIN users u ON d.client_id = u.id
                LEFT JOIN queue_config qc ON d.client_id = qc.client_id
                WHERE d.display_code = ? AND d.is_online >= 0
            ");
            $stmt->execute([$code]);
            $display = $stmt->fetch();

            if (!$display) {
                $this->error('Display not found or inactive', 404);
            }

            $clientId = $display['client_id'];
            $response = [
                'display' => [
                    'id' => $display['id'],
                    'name' => $display['name'],
                    'code' => $display['display_code'],
                    'client_name' => $display['company_name']
                ],
                'queue' => null,
                'ads' => [],
                'timestamp' => date('c')
            ];

            // Pobierz dane kolejki jeśli jest włączona
            if ($display['queue_enabled']) {
                $response['queue'] = $this->getQueueData($clientId);
            }

            // Pobierz reklamy jeśli są włączone
            if (Config::isModuleEnabled('ads')) {
                $response['ads'] = $this->getAdsData($clientId);
            }

            $this->success($response);
        } catch (Exception $e) {
            $this->log('Error getting display data: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to get display data', 500);
        }
    }

    /**
     * Heartbeat wyświetlacza
     */
    public function heartbeat($code) {
        try {
            // Znajdź wyświetlacz
            $stmt = $this->db->prepare("
                SELECT id FROM client_displays
                WHERE display_code = ? AND is_online >= 0
            ");
            $stmt->execute([$code]);
            $display = $stmt->fetch();

            if (!$display) {
                $this->error('Display not found', 404);
            }

            // Zaktualizuj ostatni heartbeat
            $stmt = $this->db->prepare("
                UPDATE client_displays
                SET last_heartbeat = datetime('now')
                WHERE id = ?
            ");
            $stmt->execute([$display['id']]);

            $this->success([
                'heartbeat_recorded' => true,
                'timestamp' => date('c')
            ]);
        } catch (Exception $e) {
            $this->log('Error recording heartbeat: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to record heartbeat', 500);
        }
    }

    /**
     * Pobierz dane kolejki dla wyświetlacza
     */
    private function getQueueData($clientId) {
        try {
            // Pobierz sale
            $stmt = $this->db->prepare("
                SELECT r.*, d.first_name, d.last_name, d.specialization, d.photo_url
                FROM queue_rooms r
                LEFT JOIN queue_doctors d ON r.doctor_id = d.id
                WHERE r.client_id = ? AND r.active = 1
                ORDER BY r.room_number, r.name
            ");
            $stmt->execute([$clientId]);
            $rooms = $stmt->fetchAll();

            $roomsData = [];
            foreach ($rooms as $room) {
                // Pobierz aktualny numer
                $stmt = $this->db->prepare("
                    SELECT * FROM queue_appointments
                    WHERE room_id = ? AND status = 'current'
                    ORDER BY id DESC LIMIT 1
                ");
                $stmt->execute([$room['id']]);
                $current = $stmt->fetch();

                // Sprawdź wizyty na kolejne dni jeśli nie ma na dzisiaj
                $waiting = [];
                $dateToCheck = date('Y-m-d'); // Dzisiaj
                $maxDays = 3; // Sprawdź dzisiaj, jutro i pojutrze
                $daysChecked = 0;

                while (empty($waiting) && $daysChecked < $maxDays) {
                    // Pobierz kolejne numery dla danej daty
                    // Pokaż tylko wizyty ze statusem 'waiting' - bez filtrowania czasowego
                    // Logika czasowa jest teraz obsługiwana przez statusy
                    $stmt = $this->db->prepare("
                        SELECT * FROM queue_appointments
                        WHERE room_id = ? AND status = 'waiting'
                        AND date(appointment_date) = ?
                        ORDER BY appointment_time ASC LIMIT 3
                    ");
                    $stmt->execute([$room['id'], $dateToCheck]);
                    $waiting = $stmt->fetchAll();

                    if (empty($waiting)) {
                        // Sprawdź następny dzień
                        $daysChecked++;
                        $nextDate = new DateTime($dateToCheck);
                        $nextDate->add(new DateInterval('P1D'));
                        $dateToCheck = $nextDate->format('Y-m-d');
                    } else {
                        // Znaleziono wizyty - przerwij pętlę
                        break;
                    }
                }

                $roomsData[] = [
                    'id' => $room['id'],
                    'name' => $room['name'],
                    'room_number' => $room['room_number'],
                    'doctor' => $room['first_name'] ? [
                        'name' => $room['first_name'] . ' ' . $room['last_name'],
                        'specialization' => $room['specialization'],
                        'photo_url' => $room['photo_url']
                    ] : null,
                    'current' => $current ? [
                        'number' => $current['appointment_time'],
                        'patient_name' => $current['patient_name']
                    ] : null,
                    'waiting' => array_map(function ($appointment) {
                        return [
                            'number' => $appointment['appointment_time'],
                            'patient_name' => $appointment['patient_name']
                        ];
                    }, $waiting)
                ];
            }

            return [
                'enabled' => true,
                'rooms' => $roomsData
            ];
        } catch (Exception $e) {
            $this->log('Error getting queue data for display: ' . $e->getMessage(), 'ERROR');
            return ['enabled' => false, 'error' => 'Failed to load queue data'];
        }
    }

    /**
     * Pobierz dane reklam dla wyświetlacza
     */
    private function getAdsData($clientId) {
        try {
            // Pobierz aktywną kampanię
            $stmt = $this->db->prepare("
                SELECT c.id, c.name, c.media_type, c.media_url, c.youtube_id, 
                       c.duration, c.description
                FROM campaign_assignments ca
                JOIN campaigns c ON ca.campaign_id = c.id
                WHERE ca.client_id = ? AND c.is_active = 1 
                      AND c.budget > c.spent
                      AND ca.is_accepted = 1
                ORDER BY RANDOM()
                LIMIT 1
            ");

            $stmt->execute([$clientId]);
            $campaign = $stmt->fetch();

            if (!$campaign) {
                return [];
            }

            // Przygotuj URL mediów
            if ($campaign['media_type'] === 'image' || $campaign['media_type'] === 'video') {
                $baseUrl = Config::getAppUrl();
                $campaign['media_url'] = rtrim($baseUrl, '/') . '/' . ltrim($campaign['media_url'], '/');
            }

            return [$campaign];
        } catch (Exception $e) {
            $this->log('Error getting ads data for display: ' . $e->getMessage(), 'ERROR');
            return [];
        }
    }
}
