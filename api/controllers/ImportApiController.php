<?php

class ImportApiController extends ApiController {
    private $importSettingModel;
    private $externalDoctorMappingModel;
    private $appointmentModel;
    private $syncDataModel;

    public function __construct() {
        parent::__construct();

        // Załaduj modele (współdzielone z admin + własny SyncData)
        require_once __DIR__ . '/../../admin/models/ImportSetting.php';
        require_once __DIR__ . '/../../admin/models/ExternalDoctorMapping.php';
        require_once __DIR__ . '/../../admin/models/Appointment.php';
        require_once __DIR__ . '/../models/SyncData.php'; // Używaj modelu z API

        $this->importSettingModel = new ImportSetting();
        $this->externalDoctorMappingModel = new ExternalDoctorMapping();
        $this->appointmentModel = new Appointment();
        $this->syncDataModel = new SyncData();
    }

    /**
     * Główny endpoint do importu danych JSON z Chrome
     * POST /api/import
     */
    public function import() {
        // Sprawdź metodę HTTP
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->error('Metoda nie jest dozwolona', 405);
            return;
        }

        // Pobierz dane JSON
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->error('Nieprawidłowy format JSON', 400);
            return;
        }

        // Sprawdź wymagane pola - obsługa obu formatów
        $syncCode = null;
        $importData = null;

        if (isset($data['syncCode']) && isset($data['syncData'])) {
            // Nowy format: {syncCode, syncData}
            $syncCode = $data['syncCode'];
            $importData = $data;
        } elseif (isset($data['sync_code']) && isset($data['data'])) {
            // Stary format z Chrome Extension: {sync_code, data}
            $syncCode = $data['sync_code'];
            $importData = [
                'syncCode' => $syncCode,
                'syncData' => $data['data']
            ];
        } else {
            $this->error('Nieprawidłowy format danych. Brak wymaganych pól: syncCode/sync_code, syncData/data', 400);
            return;
        }

        // Zapisz surowe dane JSON do historii synchronizacji
        $rawJsonData = $input;

        // Znajdź ustawienie importu
        $importSetting = $this->importSettingModel->getBySyncCode($syncCode);
        if (!$importSetting) {
            $this->error('Nieprawidłowy kod synchronizacji', 401);
            return;
        }

        // Zapisz surowe dane JSON do pliku
        $this->syncDataModel->saveData($importSetting['id'], $rawJsonData);

        // Rozpocznij import
        $result = $this->processImport($importSetting, $importData);

        if ($result['success']) {
            $stats = $result['stats'];
            $message = 'Import zakończony pomyślnie';

            // Dodaj szczegóły do wiadomości
            $details = [];
            if ($stats['appointments_created'] > 0) {
                $details[] = "utworzono {$stats['appointments_created']} wizyt";
            }
            if ($stats['appointments_updated'] > 0) {
                $details[] = "zaktualizowano {$stats['appointments_updated']} wizyt";
            }
            if ($stats['appointments_deleted'] > 0) {
                $details[] = "usunięto {$stats['appointments_deleted']} wizyt";
            }

            if (!empty($details)) {
                $message .= ': ' . implode(', ', $details);
            }

            $this->success([
                'message' => $message,
                'stats' => $stats
            ]);
        } else {
            $this->error($result['error'], 500);
        }
    }

    /**
     * Przetwarzanie danych importu
     */
    private function processImport($importSetting, $data) {
        $stats = [
            'doctors_processed' => 0,
            'doctors_mapped' => 0,
            'appointments_processed' => 0,
            'appointments_created' => 0,
            'appointments_updated' => 0,
            'appointments_deleted' => 0,
            'errors' => []
        ];

        try {
            // Rozpocznij transakcję
            $this->db->beginTransaction();

            // Loguj rozpoczęcie synchronizacji
            $syncLogId = $this->createSyncLog($importSetting['id'], 'manual');

            // Przetwarzanie danych w nowym formacie
            if (isset($data['syncData']) && isset($data['syncData']['days'])) {
                foreach ($data['syncData']['days'] as $dayData) {
                    $date = $dayData['date'] ?? date('Y-m-d');
                    $allExternalIdsForDate = []; // Zbierz wszystkie external_id dla tego dnia

                    if (isset($dayData['doctors']) && is_array($dayData['doctors'])) {
                        foreach ($dayData['doctors'] as $doctorData) {
                            $stats['doctors_processed']++;

                            // Utwórz lub zaktualizuj mapowanie lekarza
                            $mapping = $this->processDoctor($importSetting['id'], $doctorData);
                            if ($mapping) {
                                $stats['doctors_mapped']++;

                                // Przetwórz wizyty lekarza
                                if (isset($doctorData['appointments']) && is_array($doctorData['appointments'])) {
                                    foreach ($doctorData['appointments'] as $appointmentData) {
                                        $stats['appointments_processed']++;

                                        // Dodaj datę do danych wizyty
                                        $appointmentData['date'] = $date;

                                        // Zbierz external_id dla synchronizacji
                                        if (isset($appointmentData['appointmentId'])) {
                                            $allExternalIdsForDate[] = $appointmentData['appointmentId'];
                                        }

                                        $appointmentResult = $this->processAppointment(
                                            $importSetting['client_id'],
                                            $mapping,
                                            $appointmentData,
                                            $date
                                        );

                                        if ($appointmentResult['success']) {
                                            if ($appointmentResult['action'] === 'created') {
                                                $stats['appointments_created']++;
                                            } else {
                                                $stats['appointments_updated']++;
                                            }
                                        } else {
                                            $stats['errors'][] = $appointmentResult['error'];
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Synchronizacja - usuń wizyty które nie są już w JSON dla tego dnia
                    $deletedCount = $this->synchronizeAppointments($importSetting['client_id'], $date, $allExternalIdsForDate);
                    $stats['appointments_deleted'] += $deletedCount;
                }
            } else {
                throw new Exception('Nieprawidłowy format danych. Brak wymaganych pól: syncData.days');
            }

            // Zaktualizuj log synchronizacji
            $this->updateSyncLog($syncLogId, 'success', $stats);

            // Zatwierdź transakcję
            $this->db->commit();

            return [
                'success' => true,
                'stats' => $stats
            ];
        } catch (Exception $e) {
            // Cofnij transakcję
            $this->db->rollback();

            // Zaktualizuj log z błędem
            if (isset($syncLogId)) {
                $this->updateSyncLog($syncLogId, 'error', $stats, $e->getMessage());
            }

            return [
                'success' => false,
                'error' => 'Błąd podczas importu: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Przetwarzanie danych lekarza
     */
    private function processDoctor($importSettingId, $doctorData) {
        // Obsługa struktury danych zgodnej z formatem KtoOstatni_JQUERY_GLOBAL_IMPORT_FORMAT
        $externalDoctorId = $doctorData['doctorId'] ?? null;
        $externalDoctorName = $doctorData['doctorName'] ?? '';

        if (!$externalDoctorId || !$externalDoctorName) {
            return null;
        }

        // Sprawdź czy mapowanie już istnieje
        $mapping = $this->externalDoctorMappingModel->findByExternalId($importSettingId, $externalDoctorId);

        $mappingData = [
            'import_setting_id' => $importSettingId,
            'external_doctor_id' => $externalDoctorId,
            'external_doctor_name' => $externalDoctorName,
            'external_doctor_specialization' => $this->extractSpecialization($externalDoctorName)
        ];

        if ($mapping) {
            // Zaktualizuj tylko czas ostatniego widzenia, nie zmieniaj mapowania
            $this->externalDoctorMappingModel->updateLastSeen($mapping['id']);

            // Zaktualizuj nazwę lekarza tylko jeśli się zmieniła (ale zachowaj mapowanie)
            if ($mapping['external_doctor_name'] !== $externalDoctorName) {
                $updateData = [
                    'external_doctor_name' => $externalDoctorName,
                    'external_doctor_specialization' => $this->extractSpecialization($externalDoctorName)
                ];
                $this->externalDoctorMappingModel->update($mapping['id'], $updateData);
                error_log("KtoOstatni: Zaktualizowano nazwę lekarza: {$mapping['external_doctor_name']} -> {$externalDoctorName}");
            }

            return $mapping;
        } else {
            // Utwórz nowe mapowanie
            $this->externalDoctorMappingModel->create($mappingData);
            return $this->externalDoctorMappingModel->findByExternalId($importSettingId, $externalDoctorId);
        }
    }



    /**
     * Przetwarzanie wizyt
     */
    private function processAppointment($clientId, $doctorMapping, $appointmentData, $date) {
        error_log("KtoOstatni: Przetwarzam wizytę dla lekarza: {$doctorMapping['external_doctor_name']}");

        // Sprawdź czy lekarz jest zmapowany
        if (!$doctorMapping['is_mapped'] || !$doctorMapping['system_doctor_id']) {
            error_log("KtoOstatni: Lekarz nie jest zmapowany: {$doctorMapping['external_doctor_name']} (is_mapped: {$doctorMapping['is_mapped']}, system_doctor_id: {$doctorMapping['system_doctor_id']})");
            return [
                'success' => false,
                'error' => 'Lekarz nie jest zmapowany: ' . $doctorMapping['external_doctor_name']
            ];
        }

        error_log("KtoOstatni: Lekarz zmapowany: {$doctorMapping['external_doctor_name']} -> system ID: {$doctorMapping['system_doctor_id']}");

        // Parsuj dane wizyty z nowego formatu
        $appointmentId = $appointmentData['appointmentId'] ?? null;
        $patientFirstName = $appointmentData['patientFirstName'] ?? '';
        $patientLastName = $appointmentData['patientLastName'] ?? '';
        $patientName = trim($patientFirstName . ' ' . $patientLastName);
        $appointmentStart = $appointmentData['appointmentStart'] ?? '';
        $appointmentEnd = $appointmentData['appointmentEnd'] ?? '';
        $appointmentDuration = $appointmentData['appointmentDuration'] ?? 20;

        // Dodatkowe pola (opcjonalne) - zgodnie z dokumentacją formatu JSON
        $service = $appointmentData['service'] ?? '';
        $office = $appointmentData['office'] ?? '';
        $type = $appointmentData['type'] ?? 'wizyta';

        if (!$appointmentId || !$patientName || !$appointmentStart) {
            return [
                'success' => false,
                'error' => 'Brak wymaganych danych wizyty'
            ];
        }

        // Stwórz zakres czasu dla kompatybilności
        $timeRange = $appointmentStart;
        if ($appointmentEnd) {
            $timeRange .= ' - ' . $appointmentEnd;
        }

        // Parsuj czas
        $timeData = [
            'date' => $date,
            'start_time' => $appointmentStart,
            'end_time' => $appointmentEnd ?: $this->calculateEndTime($appointmentStart, $appointmentDuration)
        ];

        // Sprawdź czy wizyta już istnieje
        $existingAppointment = $this->findExistingAppointment($clientId, $appointmentId);

        $appointmentDataToSave = [
            'client_id' => $clientId,
            'doctor_id' => $doctorMapping['system_doctor_id'],
            'external_id' => $appointmentId,
            'patient_name' => $patientName,
            'service_name' => $service,
            'appointment_date' => $timeData['date'],
            'appointment_time' => $timeData['start_time'],
            'end_time' => $timeData['end_time'],
            'office' => $office,
            'type' => $type,
            'status' => 'waiting'
        ];

        if ($existingAppointment) {
            // Zaktualizuj istniejącą wizytę
            $this->appointmentModel->updateFromImport($existingAppointment['id'], $appointmentDataToSave);
            return [
                'success' => true,
                'action' => 'updated'
            ];
        } else {
            // Utwórz nową wizytę
            $this->appointmentModel->createFromImport($appointmentDataToSave);
            return [
                'success' => true,
                'action' => 'created'
            ];
        }
    }

    /**
     * Pomocnicze metody
     */
    private function extractSpecialization($doctorName) {
        // Wyciągnij specjalizację z nazwy lekarza
        if (preg_match('/\b(ginekolog|kardiolog|pediatra|internista|dermatolog|neurolog|ortopeda|urolog|okulista|laryngolog)\b/i', $doctorName, $matches)) {
            return ucfirst(strtolower($matches[1]));
        }
        return null;
    }



    private function calculateEndTime($startTime, $durationMinutes = 20) {
        // Oblicz czas zakończenia na podstawie czasu rozpoczęcia i długości wizyty
        list($hours, $minutes) = explode(':', $startTime);
        $hours = (int)$hours;
        $minutes = (int)$minutes;

        $endMinutes = $minutes + $durationMinutes;
        $endHours = $hours + floor($endMinutes / 60);
        $endMinutes = $endMinutes % 60;

        return sprintf('%02d:%02d', $endHours, $endMinutes);
    }



    private function findExistingAppointment($clientId, $externalId) {
        $stmt = $this->db->prepare("
            SELECT * FROM queue_appointments
            WHERE client_id = ? AND external_id = ?
        ");
        $stmt->execute([$clientId, $externalId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    private function createSyncLog($importSettingId, $syncType) {
        $stmt = $this->db->prepare("
            INSERT INTO sync_logs (import_setting_id, sync_type, status, started_at)
            VALUES (?, ?, 'running', CURRENT_TIMESTAMP)
        ");
        $stmt->execute([$importSettingId, $syncType]);
        return $this->db->lastInsertId();
    }

    private function updateSyncLog($syncLogId, $status, $stats, $errorMessage = null) {
        // Sprawdź czy kolumna records_deleted istnieje i dodaj ją jeśli nie
        $this->ensureRecordsDeletedColumn();

        $stmt = $this->db->prepare("
            UPDATE sync_logs SET
                status = ?,
                records_processed = ?,
                records_created = ?,
                records_updated = ?,
                records_deleted = ?,
                error_message = ?,
                completed_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");

        $stmt->execute([
            $status,
            $stats['appointments_processed'] ?? 0,
            $stats['appointments_created'] ?? 0,
            $stats['appointments_updated'] ?? 0,
            $stats['appointments_deleted'] ?? 0,
            $errorMessage,
            $syncLogId
        ]);
    }

    private function ensureRecordsDeletedColumn() {
        try {
            // Sprawdź czy kolumna records_deleted istnieje
            $stmt = $this->db->prepare("PRAGMA table_info(sync_logs)");
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $hasRecordsDeleted = false;
            foreach ($columns as $column) {
                if ($column['name'] === 'records_deleted') {
                    $hasRecordsDeleted = true;
                    break;
                }
            }

            // Dodaj kolumnę jeśli nie istnieje
            if (!$hasRecordsDeleted) {
                $this->db->exec("ALTER TABLE sync_logs ADD COLUMN records_deleted INTEGER DEFAULT 0");
                error_log("KtoOstatni: Dodano kolumnę records_deleted do tabeli sync_logs");
            }
        } catch (Exception $e) {
            // Ignoruj błędy - kolumna prawdopodobnie już istnieje
            error_log("KtoOstatni: Błąd podczas dodawania kolumny records_deleted: " . $e->getMessage());
        }
    }

    /**
     * Endpoint do sprawdzania statusu importu
     * GET /api/import/status/{syncCode}
     */
    public function status($syncCode = null) {
        if (!$syncCode) {
            $this->error('Brak kodu synchronizacji', 400);
            return;
        }

        $importSetting = $this->importSettingModel->getBySyncCode($syncCode);
        if (!$importSetting) {
            $this->error('Nieprawidłowy kod synchronizacji', 401);
            return;
        }

        // Pobierz ostatnie logi synchronizacji
        $stmt = $this->db->prepare("
            SELECT * FROM sync_logs
            WHERE import_setting_id = ?
            ORDER BY started_at DESC
            LIMIT 10
        ");
        $stmt->execute([$importSetting['id']]);
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Pobierz statystyki mapowań
        $mappedDoctors = $this->externalDoctorMappingModel->getMapped($importSetting['id']);
        $unmappedDoctors = $this->externalDoctorMappingModel->getUnmapped($importSetting['id']);

        $this->success([
            'import_setting' => [
                'id' => $importSetting['id'],
                'system_name' => $importSetting['system_name'],
                'is_active' => $importSetting['is_active'],
                'last_sync' => $logs[0]['started_at'] ?? null
            ],
            'doctor_mappings' => [
                'mapped' => count($mappedDoctors),
                'unmapped' => count($unmappedDoctors),
                'total' => count($mappedDoctors) + count($unmappedDoctors)
            ],
            'recent_syncs' => $logs
        ]);
    }

    /**
     * Endpoint do automatycznego mapowania lekarzy
     * POST /api/import/auto-map/{syncCode}
     */
    public function autoMap($syncCode = null) {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->error('Metoda nie jest dozwolona', 405);
            return;
        }

        if (!$syncCode) {
            $this->error('Brak kodu synchronizacji', 400);
            return;
        }

        $importSetting = $this->importSettingModel->getBySyncCode($syncCode);
        if (!$importSetting) {
            $this->error('Nieprawidłowy kod synchronizacji', 401);
            return;
        }

        $result = $this->performAutoMapping($importSetting);

        $this->success([
            'message' => 'Automatyczne mapowanie zakończone',
            'mapped_count' => $result['mapped_count'],
            'suggestions' => $result['suggestions']
        ]);
    }

    private function performAutoMapping($importSetting) {
        $unmappedDoctors = $this->externalDoctorMappingModel->getUnmapped($importSetting['id']);
        $systemDoctors = $this->externalDoctorMappingModel->getAvailableSystemDoctors($importSetting['client_id']);

        $mappedCount = 0;
        $suggestions = [];

        foreach ($unmappedDoctors as $externalDoctor) {
            $bestMatch = $this->findBestDoctorMatch($externalDoctor, $systemDoctors);

            if ($bestMatch && $bestMatch['confidence'] > 0.8) {
                // Automatycznie mapuj jeśli pewność > 80%
                $this->externalDoctorMappingModel->update($externalDoctor['id'], [
                    'external_doctor_name' => $externalDoctor['external_doctor_name'],
                    'external_doctor_specialization' => $externalDoctor['external_doctor_specialization'],
                    'system_doctor_id' => $bestMatch['doctor']['id'],
                    'is_mapped' => 1
                ]);
                $mappedCount++;
            } elseif ($bestMatch && $bestMatch['confidence'] > 0.5) {
                // Dodaj do sugestii jeśli pewność > 50%
                $suggestions[] = [
                    'external_doctor' => $externalDoctor,
                    'suggested_doctor' => $bestMatch['doctor'],
                    'confidence' => $bestMatch['confidence']
                ];
            }
        }

        return [
            'mapped_count' => $mappedCount,
            'suggestions' => $suggestions
        ];
    }

    private function findBestDoctorMatch($externalDoctor, $systemDoctors) {
        $bestMatch = null;
        $bestScore = 0;

        $externalName = strtolower($externalDoctor['external_doctor_name']);

        foreach ($systemDoctors as $systemDoctor) {
            $systemName = strtolower($systemDoctor['first_name'] . ' ' . $systemDoctor['last_name']);

            // Oblicz podobieństwo nazw
            $nameScore = $this->calculateNameSimilarity($externalName, $systemName);

            // Bonus za zgodność specjalizacji
            $specializationScore = 0;
            if ($externalDoctor['external_doctor_specialization'] && $systemDoctor['specialization']) {
                if (
                    stripos($externalDoctor['external_doctor_specialization'], $systemDoctor['specialization']) !== false ||
                    stripos($systemDoctor['specialization'], $externalDoctor['external_doctor_specialization']) !== false
                ) {
                    $specializationScore = 0.3;
                }
            }

            $totalScore = $nameScore + $specializationScore;

            if ($totalScore > $bestScore) {
                $bestScore = $totalScore;
                $bestMatch = [
                    'doctor' => $systemDoctor,
                    'confidence' => $totalScore
                ];
            }
        }

        return $bestMatch;
    }

    private function calculateNameSimilarity($name1, $name2) {
        // Usuń tytuły i stopnie naukowe
        $name1 = preg_replace('/\b(dr|lek|prof|mgr|inż)\.?\s*/i', '', $name1);
        $name2 = preg_replace('/\b(dr|lek|prof|mgr|inż)\.?\s*/i', '', $name2);

        // Usuń "n. med." itp.
        $name1 = preg_replace('/\bn\.\s*med\.?\s*/i', '', $name1);
        $name2 = preg_replace('/\bn\.\s*med\.?\s*/i', '', $name2);

        // Oblicz podobieństwo Levenshteina
        $maxLen = max(strlen($name1), strlen($name2));
        if ($maxLen == 0) return 0;

        $distance = levenshtein($name1, $name2);
        return 1 - ($distance / $maxLen);
    }

    /**
     * Synchronizacja wizyt - usuwa wizyty które nie są już w JSON
     */
    private function synchronizeAppointments($clientId, $date, $externalIds) {
        // Policz ile wizyt zostanie usuniętych (dla statystyk)
        $deletedCount = $this->appointmentModel->countDeletedAppointments($clientId, $date, $externalIds);

        // Usuń wizyty które nie są w liście external_id
        $this->appointmentModel->deleteNotInExternalIds($clientId, $date, $externalIds);

        if ($deletedCount > 0) {
            error_log("KtoOstatni: Usunięto $deletedCount wizyt dla dnia $date (klient: $clientId)");
        }

        return $deletedCount;
    }
}
