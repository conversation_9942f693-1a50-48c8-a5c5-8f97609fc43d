{"sync_code": "igab1234567890123", "data": {"days": [{"date": "2025-09-01", "doctors": [{"doctorId": "**********", "doctorName": "dermatolog dr <PERSON>-Wójcik", "appointments": []}, {"doctorId": "**********", "doctorName": "<PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "107901", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Szeremeta", "appointmentStart": "09:00", "appointmentEnd": "09:20", "appointmentDuration": 20}, {"appointmentId": "108059", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Nawrocka- Bar", "appointmentStart": "09:20", "appointmentEnd": "09:40", "appointmentDuration": 20}, {"appointmentId": "106740", "patientFirstName": "<PERSON>", "patientLastName": "Napieralska", "appointmentStart": "10:00", "appointmentEnd": "10:20", "appointmentDuration": 20}, {"appointmentId": "107783", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Wołoszyńska", "appointmentStart": "11:00", "appointmentEnd": "11:20", "appointmentDuration": 20}, {"appointmentId": "108091", "patientFirstName": "Paul<PERSON>", "patientLastName": "Hamerlińska", "appointmentStart": "14:00", "appointmentEnd": "14:20", "appointmentDuration": 20}]}, {"doctorId": "43", "doctorName": "genetyk dr n.med. <PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "105", "doctorName": "ginekolog dr <PERSON><PERSON>", "appointments": []}, {"doctorId": "114", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "129", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "72", "doctorName": "ginekolog dr <PERSON>-<PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "10", "doctorName": "ginekolog dr n.med. Małgorzata Ole<PERSON>k-And<PERSON>sz<PERSON>ak", "appointments": []}, {"doctorId": "20", "doctorName": "ginekolog dr <PERSON>", "appointments": [{"appointmentId": "104848", "patientFirstName": "Sztucka", "patientLastName": "Katarzyna", "appointmentStart": "09:00", "appointmentEnd": "09:20", "appointmentDuration": 20}, {"appointmentId": "106247", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "09:20", "appointmentEnd": "09:40", "appointmentDuration": 20}, {"appointmentId": "104980", "patientFirstName": "Magdalena", "patientLastName": "Gnosowska", "appointmentStart": "09:40", "appointmentEnd": "10:00", "appointmentDuration": 20}, {"appointmentId": "106937", "patientFirstName": "Samant<PERSON>", "patientLastName": "Bielak", "appointmentStart": "10:00", "appointmentEnd": "10:20", "appointmentDuration": 20}, {"appointmentId": "106939", "patientFirstName": "Daria", "patientLastName": "Majdańska", "appointmentStart": "10:20", "appointmentEnd": "10:40", "appointmentDuration": 20}, {"appointmentId": "106940", "patientFirstName": "Daria", "patientLastName": "Dziewirz", "appointmentStart": "10:40", "appointmentEnd": "11:00", "appointmentDuration": 20}, {"appointmentId": "107179", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "11:00", "appointmentEnd": "11:20", "appointmentDuration": 20}, {"appointmentId": "107316", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Wdowikowska", "appointmentStart": "11:20", "appointmentEnd": "11:40", "appointmentDuration": 20}, {"appointmentId": "105007", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Ćwiklińska", "appointmentStart": "11:40", "appointmentEnd": "12:00", "appointmentDuration": 20}, {"appointmentId": "107426", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "12:00", "appointmentEnd": "12:20", "appointmentDuration": 20}, {"appointmentId": "107467", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Stępień", "appointmentStart": "12:20", "appointmentEnd": "12:40", "appointmentDuration": 20}, {"appointmentId": "107647", "patientFirstName": "A<PERSON><PERSON>z<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "12:40", "appointmentEnd": "13:00", "appointmentDuration": 20}, {"appointmentId": "108658", "patientFirstName": "Svietlana", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "13:00", "appointmentEnd": "13:20", "appointmentDuration": 20}, {"appointmentId": "106228", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Kłodzińska", "appointmentStart": "13:20", "appointmentEnd": "13:40", "appointmentDuration": 20}, {"appointmentId": "104902", "patientFirstName": "Honorata", "patientLastName": "Zakrent", "appointmentStart": "13:40", "appointmentEnd": "14:00", "appointmentDuration": 20}]}, {"doctorId": "83", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "19", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "106923", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Telecka", "appointmentStart": "10:20", "appointmentEnd": "10:40", "appointmentDuration": 20}, {"appointmentId": "107095", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "10:40", "appointmentEnd": "11:00", "appointmentDuration": 20}, {"appointmentId": "107854", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Len<PERSON>", "appointmentStart": "11:00", "appointmentEnd": "11:20", "appointmentDuration": 20}, {"appointmentId": "108094", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "11:20", "appointmentEnd": "11:40", "appointmentDuration": 20}, {"appointmentId": "108418", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "11:40", "appointmentEnd": "12:00", "appointmentDuration": 20}, {"appointmentId": "108577", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Myszko-Nowakowska", "appointmentStart": "12:00", "appointmentEnd": "12:20", "appointmentDuration": 20}, {"appointmentId": "108633", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Mak", "appointmentStart": "12:20", "appointmentEnd": "12:40", "appointmentDuration": 20}, {"appointmentId": "108609", "patientFirstName": "Victoria", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "13:20", "appointmentEnd": "13:40", "appointmentDuration": 20}, {"appointmentId": "107413", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "pacjent", "appointmentStart": "13:40", "appointmentEnd": "15:00", "appointmentDuration": 80}]}, {"doctorId": "82", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "108713", "patientFirstName": "Anastasiia", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "15:00", "appointmentEnd": "15:20", "appointmentDuration": 20}, {"appointmentId": "108715", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "15:20", "appointmentEnd": "15:40", "appointmentDuration": 20}, {"appointmentId": "108714", "patientFirstName": "Katarzyna", "patientLastName": "<PERSON><PERSON>ę<PERSON>a", "appointmentStart": "15:40", "appointmentEnd": "16:00", "appointmentDuration": 20}, {"appointmentId": "108697", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "16:00", "appointmentEnd": "16:20", "appointmentDuration": 20}, {"appointmentId": "108672", "patientFirstName": "Yuliia", "patientLastName": "Hrechkina", "appointmentStart": "16:20", "appointmentEnd": "16:40", "appointmentDuration": 20}, {"appointmentId": "108659", "patientFirstName": "Natalia", "patientLastName": "Vasylevska", "appointmentStart": "16:40", "appointmentEnd": "17:00", "appointmentDuration": 20}, {"appointmentId": "108622", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "17:00", "appointmentEnd": "17:20", "appointmentDuration": 20}, {"appointmentId": "108377", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "17:40", "appointmentEnd": "18:00", "appointmentDuration": 20}, {"appointmentId": "108087", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "18:00", "appointmentEnd": "18:20", "appointmentDuration": 20}, {"appointmentId": "108273", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Dusianok", "appointmentStart": "18:20", "appointmentEnd": "18:40", "appointmentDuration": 20}, {"appointmentId": "108067", "patientFirstName": "Natalia", "patientLastName": "Rechytska", "appointmentStart": "18:40", "appointmentEnd": "19:00", "appointmentDuration": 20}]}, {"doctorId": "110", "doctorName": "ginekolog dziecięcy dr Agnieszka Tyszko-Tymińska", "appointments": []}, {"doctorId": "**********", "doctorName": "hepa<PERSON><PERSON>, specjalista chorób zakaźnych dr <PERSON><PERSON>", "appointments": []}]}]}}