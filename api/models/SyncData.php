<?php

class SyncData {
    private $syncDir;

    public function __construct() {
        // Katalog do przechowywania plików synchronizacji w API
        $this->syncDir = dirname(__DIR__) . '/data/sync';

        // Utwórz katalog, jeśli nie istnieje
        if (!is_dir($this->syncDir)) {
            mkdir($this->syncDir, 0755, true);
        }
    }

    /**
     * Zapisz dane synchronizacji do pliku
     */
    public function saveData($importSettingId, $rawData) {
        // Utwórz katalog dla danego ustawienia importu, jeśli nie istnieje
        $settingDir = $this->syncDir . '/' . $importSettingId;
        if (!is_dir($settingDir)) {
            mkdir($settingDir, 0755, true);
        }

        // Generuj nazwę pliku z datą i czasem
        $timestamp = date('Y-m-d_H-i-s');
        $filename = $timestamp . '.json';
        $filepath = $settingDir . '/' . $filename;

        // Zapisz dane do pliku
        file_put_contents($filepath, $rawData);

        // Zwróć informacje o zapisanym pliku
        return [
            'id' => $timestamp,
            'import_setting_id' => $importSettingId,
            'filepath' => $filepath,
            'filename' => $filename,
            'created_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Pobierz ostatnie dane synchronizacji
     */
    public function getLatestData($importSettingId) {
        $settingDir = $this->syncDir . '/' . $importSettingId;
        if (!is_dir($settingDir)) {
            return null;
        }

        // Pobierz listę plików JSON
        $files = glob($settingDir . '/*.json');

        // Sortuj pliki według daty modyfikacji (od najnowszych)
        usort($files, function ($a, $b) {
            return filemtime($b) - filemtime($a);
        });

        // Jeśli nie ma plików, zwróć null
        if (empty($files)) {
            return null;
        }

        // Pobierz najnowszy plik
        $latestFile = $files[0];
        $filename = basename($latestFile);
        $timestamp = str_replace('.json', '', $filename);

        return [
            'id' => $timestamp,
            'import_setting_id' => $importSettingId,
            'filepath' => $latestFile,
            'filename' => $filename,
            'created_at' => date('Y-m-d H:i:s', filemtime($latestFile)),
            'data_size' => filesize($latestFile),
            'raw_data' => file_get_contents($latestFile)
        ];
    }

    /**
     * Pobierz historię synchronizacji
     */
    public function getHistory($importSettingId, $limit = 10) {
        $settingDir = $this->syncDir . '/' . $importSettingId;
        if (!is_dir($settingDir)) {
            return [];
        }

        // Pobierz listę plików JSON
        $files = glob($settingDir . '/*.json');

        // Sortuj pliki według daty modyfikacji (od najnowszych)
        usort($files, function ($a, $b) {
            return filemtime($b) - filemtime($a);
        });

        // Ogranicz liczbę plików
        $files = array_slice($files, 0, $limit);

        // Przygotuj dane o plikach
        $history = [];
        foreach ($files as $file) {
            $filename = basename($file);
            $timestamp = str_replace('.json', '', $filename);

            $history[] = [
                'id' => $timestamp,
                'import_setting_id' => $importSettingId,
                'filepath' => $file,
                'filename' => $filename,
                'created_at' => date('Y-m-d H:i:s', filemtime($file)),
                'data_size' => filesize($file)
            ];
        }

        return $history;
    }

    /**
     * Pobierz dane synchronizacji po ID (timestamp)
     */
    public function getById($importSettingId, $id) {
        $settingDir = $this->syncDir . '/' . $importSettingId;
        $filepath = $settingDir . '/' . $id . '.json';

        if (!file_exists($filepath)) {
            return null;
        }

        return [
            'id' => $id,
            'import_setting_id' => $importSettingId,
            'filepath' => $filepath,
            'filename' => $id . '.json',
            'created_at' => date('Y-m-d H:i:s', filemtime($filepath)),
            'data_size' => filesize($filepath),
            'raw_data' => file_get_contents($filepath)
        ];
    }

    /**
     * Usuń stare dane synchronizacji (starsze niż X dni)
     */
    public function cleanupOldData($days = 30) {
        // Przejdź przez wszystkie katalogi ustawień
        $settingDirs = glob($this->syncDir . '/*', GLOB_ONLYDIR);
        $deletedCount = 0;

        foreach ($settingDirs as $settingDir) {
            // Pobierz listę plików JSON
            $files = glob($settingDir . '/*.json');

            // Oblicz datę graniczną
            $cutoffTime = time() - ($days * 24 * 60 * 60);

            // Usuń pliki starsze niż data graniczna
            foreach ($files as $file) {
                if (filemtime($file) < $cutoffTime) {
                    unlink($file);
                    $deletedCount++;
                }
            }
        }

        return $deletedCount;
    }
}
