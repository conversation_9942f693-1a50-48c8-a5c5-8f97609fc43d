# 🗄 Baza Danych - Struktura i Zarządzanie

## 🎯 **Przegląd**

System KtoOstatni używa bazy danych SQLite do przechowywania informacji o lekarzach, wizytach i konfiguracji.

## 📊 **Struktura tabel**

### **1. <PERSON><PERSON><PERSON> (`queue_doctors`)**
```sql
CREATE TABLE queue_doctors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    specialization TEXT,
    photo_url TEXT,
    active INTEGER DEFAULT 1,
    default_room_id INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**Opis pól:**
- `id` - Unikalny identyfikator lekarza
- `client_id` - ID klienta/przych<PERSON>ni
- `first_name`, `last_name` - <PERSON><PERSON><PERSON> i nazwisko
- `specialization` - Specjalizacja medyczna
- `photo_url` - Ś<PERSON>żka do zdjęcia lekarza
- `active` - <PERSON><PERSON> le<PERSON>z jest aktywny (1/0)

### **2. Wizyty (`queue_appointments`)**
```sql
CREATE TABLE queue_appointments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    doctor_id INTEGER NOT NULL,
    patient_name TEXT NOT NULL,
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    end_time TIME,
    duration INTEGER,
    status TEXT DEFAULT 'scheduled',
    external_id TEXT,
    service_name TEXT,
    office TEXT,
    type TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (doctor_id) REFERENCES queue_doctors(id)
);
```

**Opis pól:**
- `external_id` - ID wizyty z systemu zewnętrznego (iGabinet)
- `status` - Status wizyty (scheduled, in_progress, completed)
- `service_name` - Nazwa usługi medycznej
- `office` - Numer gabinetu

### **3. Ustawienia importu (`import_settings`)**
```sql
CREATE TABLE import_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sync_code TEXT UNIQUE NOT NULL,
    system_name TEXT NOT NULL,
    client_id INTEGER NOT NULL,
    is_active INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### **4. Mapowanie lekarzy (`external_doctor_mappings`)**
```sql
CREATE TABLE external_doctor_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    import_setting_id INTEGER NOT NULL,
    external_doctor_id TEXT NOT NULL,
    external_doctor_name TEXT NOT NULL,
    system_doctor_id INTEGER,
    is_mapped INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (import_setting_id) REFERENCES import_settings(id),
    FOREIGN KEY (system_doctor_id) REFERENCES queue_doctors(id)
);
```

### **5. Logi synchronizacji (`sync_logs`)**
```sql
CREATE TABLE sync_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    import_setting_id INTEGER NOT NULL,
    status TEXT NOT NULL,
    records_processed INTEGER DEFAULT 0,
    records_created INTEGER DEFAULT 0,
    records_updated INTEGER DEFAULT 0,
    records_deleted INTEGER DEFAULT 0,
    error_message TEXT,
    started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    FOREIGN KEY (import_setting_id) REFERENCES import_settings(id)
);
```

## 🔧 **Zarządzanie bazą**

### **Lokalizacja:**
- Główna baza: `database/reklama.db`
- Backup: `database/reklama_backup_chrome_extension_updated.db`

### **Inicjalizacja:**
```bash
# Uruchomienie skryptu inicjalizacyjnego z aktualnymi danymi
php admin/init_db.php
```

**Uwaga:** Plik `init_db.php` został zaktualizowany i zawiera:
- ✅ Aktualne dane użytkowników z bazy produkcyjnej
- ✅ Wszystkich lekarzy z ich specjalizacjami i kodami dostępu
- ✅ Konfigurację sal i gabinetów
- ✅ Ustawienia importu z systemu iGabinet
- ✅ Mapowania lekarzy między systemami
- ❌ **Nie zawiera umówionych wizyt** (zgodnie z wymaganiami)

### **Backup:**
```bash
# Tworzenie kopii zapasowej
cp database/reklama.db database/backup_$(date +%Y%m%d_%H%M%S).db

# Przywracanie z backupu
cp database/backup_20250827_140000.db database/reklama.db
```

## 📈 **Zapytania użytkowe**

### **Statystyki wizyt:**
```sql
-- Liczba wizyt dzisiaj
SELECT COUNT(*) as wizyty_dzisiaj 
FROM queue_appointments 
WHERE appointment_date = DATE('now');

-- Wizyty według lekarzy
SELECT 
    d.first_name || ' ' || d.last_name as lekarz,
    COUNT(a.id) as liczba_wizyt
FROM queue_doctors d
LEFT JOIN queue_appointments a ON d.id = a.doctor_id 
    AND a.appointment_date = DATE('now')
GROUP BY d.id
ORDER BY liczba_wizyt DESC;
```

### **Status synchronizacji:**
```sql
-- Ostatnie synchronizacje
SELECT 
    status,
    records_processed,
    records_created,
    records_updated,
    records_deleted,
    started_at
FROM sync_logs 
ORDER BY started_at DESC 
LIMIT 10;
```

### **Mapowania lekarzy:**
```sql
-- Lekarze zmapowani
SELECT 
    edm.external_doctor_name as "Lekarz w iGabinet",
    qd.first_name || ' ' || qd.last_name as "Lekarz w systemie",
    edm.is_mapped as "Zmapowany"
FROM external_doctor_mappings edm
LEFT JOIN queue_doctors qd ON edm.system_doctor_id = qd.id
ORDER BY edm.external_doctor_name;
```

## 🔒 **Bezpieczeństwo**

### **Uprawnienia plików:**
```bash
# Ustaw odpowiednie uprawnienia
chmod 644 database/reklama.db
chown www-data:www-data database/reklama.db
```

### **Backup automatyczny:**
```bash
# Dodaj do crontab
0 2 * * * cp /path/to/database/reklama.db /path/to/backups/reklama_$(date +\%Y\%m\%d).db
```

## 🧹 **Konserwacja**

### **Czyszczenie starych danych:**
```sql
-- Usuń wizyty starsze niż 30 dni
DELETE FROM queue_appointments 
WHERE appointment_date < DATE('now', '-30 days');

-- Usuń stare logi (starsze niż 90 dni)
DELETE FROM sync_logs 
WHERE started_at < DATETIME('now', '-90 days');
```

### **Optymalizacja:**
```sql
-- Przebuduj indeksy
REINDEX;

-- Kompaktuj bazę
VACUUM;

-- Aktualizuj statystyki
ANALYZE;
```

## 📊 **Monitoring**

### **Rozmiar bazy:**
```bash
# Sprawdź rozmiar pliku bazy
ls -lh database/reklama.db

# Sprawdź rozmiar tabel
sqlite3 database/reklama.db "
SELECT 
    name,
    COUNT(*) as records
FROM sqlite_master 
WHERE type='table' 
GROUP BY name;
"
```

### **Integralność danych:**
```sql
-- Sprawdź integralność
PRAGMA integrity_check;

-- Sprawdź klucze obce
PRAGMA foreign_key_check;
```

---
*Zobacz także: [API Dokumentacja](./12-API-REFERENCE.md)*
