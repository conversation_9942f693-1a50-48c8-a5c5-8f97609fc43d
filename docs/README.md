# 📋 KtoOstatni - Dokumentacja Systemu

## 🎯 **Przegląd Systemu**

KtoOstatni to zintegrowany system reklamowy i kolejkowy dla przychodni medycznych z automatyczną synchronizacją z systemem iGabinet.

### **Główne komponenty:**
- 🏥 **System kolejkowy** - zarządzanie wizytami i kolejkami pacjentów
- 📺 **System reklamowy** - wyświetlanie kampanii reklamowych w poczekalni
- 🔄 **Synchronizacja iGabinet** - automatyczny import wizyt z systemu iGabinet
- 🌐 **Rozszerzenie Chrome** - pobieranie danych z iGabinet
- 📱 **PWA** - aplikacja mobilna dla lekarzy
- 🖥️ **Aplikacja wyświetlacza** - dedykowana aplikacja dla ekranów w poczekalni

## 📖 **Dokumentacja według kategorii:**

### **🏥 System Kolejkowy**
- [System Kolejkowy - Przegląd](./01-QUEUE-SYSTEM.md) - Główne funkcje systemu kolejkowego
- [Zarządzanie Lekarzami](./02-DOCTORS-MANAGEMENT.md) - Dodawanie i konfiguracja lekarzy
- [Zarządzanie Wizytami](./03-APPOINTMENTS-MANAGEMENT.md) - Obsługa wizyt i kolejek

### **🔄 Import i Synchronizacja**
- [System Importu - Przegląd](./04-IMPORT-SYSTEM.md) - Ogólny opis systemu importu
- [Synchronizacja z iGabinet](./05-IGABINET-SYNC.md) - Konfiguracja i zasady synchronizacji
- [Mapowanie Lekarzy](./06-DOCTOR-MAPPING.md) - Mapowanie lekarzy między systemami

### **🌐 Rozszerzenie Chrome**
- [Instalacja Rozszerzenia](./07-CHROME-EXTENSION-INSTALL.md) - Instrukcja instalacji
- [Konfiguracja Rozszerzenia](./08-CHROME-EXTENSION-CONFIG.md) - Ustawienia i konfiguracja
- [Rozwiązywanie Problemów](./09-CHROME-EXTENSION-TROUBLESHOOTING.md) - Debugowanie i błędy

### **🛠 Administracja**
- [Konfiguracja Systemu](./10-SYSTEM-CONFIG.md) - Ustawienia systemowe
- [Baza Danych](./11-DATABASE.md) - Struktura i zarządzanie bazą
- [API Dokumentacja](./12-API-REFERENCE.md) - Dokumentacja API

### **📱 PWA i Frontend**
- [Aplikacja PWA](./13-PWA-APP.md) - Aplikacja mobilna dla pacjentów
- [Interface Użytkownika](./14-USER-INTERFACE.md) - Opis interfejsów

### **🔧 Rozwój i Utrzymanie**
- [Przewodnik Developera](./15-DEVELOPER-GUIDE.md) - Informacje dla programistów
- [Changelog](./16-CHANGELOG.md) - Historia zmian
- [FAQ](./17-FAQ.md) - Często zadawane pytania

## 🚀 **Szybki Start**

1. **Instalacja systemu:** Zobacz [Konfiguracja Systemu](./10-SYSTEM-CONFIG.md)
2. **Dodanie lekarzy:** Zobacz [Zarządzanie Lekarzami](./02-DOCTORS-MANAGEMENT.md)
3. **Konfiguracja importu:** Zobacz [System Importu](./04-IMPORT-SYSTEM.md)
4. **Instalacja rozszerzenia:** Zobacz [Instalacja Rozszerzenia](./07-CHROME-EXTENSION-INSTALL.md)

## 📞 **Wsparcie**

W przypadku problemów sprawdź:
1. [FAQ](./17-FAQ.md) - najczęstsze problemy
2. [Rozwiązywanie Problemów](./09-CHROME-EXTENSION-TROUBLESHOOTING.md) - problemy z rozszerzeniem
3. [Przewodnik Developera](./15-DEVELOPER-GUIDE.md) - problemy techniczne

## 🏗 **Wymagania Systemowe**

- PHP 8.0+
- SQLite 3
- Serwer web (Apache/Nginx)
- Nowoczesna przeglądarka z obsługą JavaScript

## 📁 **Struktura Projektu (po refaktoryzacji MVC)**

```
ktoostatni/
├── config.php          # Główny plik konfiguracyjny dla wszystkich aplikacji
├── index.php           # Router główny kierujący żądania do aplikacji
├── admin/              # Aplikacja administracyjna (MVC)
│   ├── controllers/    # Kontrolery aplikacji admin
│   ├── models/         # Modele danych
│   ├── views/          # Widoki i szablony
│   ├── core/           # Klasy podstawowe (Router, Database, Controller)
│   ├── helpers/        # Klasy pomocnicze (UrlHelper)
│   ├── assets/         # Pliki statyczne (CSS, JS, obrazy)
│   ├── index.php       # Punkt wejścia aplikacji admin
│   └── init_db.php     # Inicjalizacja bazy danych z aktualnymi danymi
├── api/                # Aplikacja API (MVC)
│   ├── controllers/    # Kontrolery API
│   ├── core/           # Rdzeń API
│   └── index.php       # Punkt wejścia API
├── display/            # Aplikacja wyświetlacza (MVC)
│   ├── controllers/    # Kontrolery wyświetlacza
│   ├── models/         # Modele wyświetlacza
│   └── index.php       # Punkt wejścia wyświetlacza
├── pwa/                # Progressive Web App dla lekarzy
│   ├── index.html      # Główna strona PWA
│   ├── app.js          # Logika PWA
│   ├── manifest.json   # Manifest PWA
│   └── sw.js           # Service Worker
├── chrome/             # Rozszerzenie Chrome
│   ├── manifest.json   # Manifest rozszerzenia
│   ├── content.js      # Skrypt zawartości
│   └── background.js   # Skrypt tła
├── database/           # Bazy danych SQLite
│   └── reklama.db      # Główna baza danych
├── uploads/            # Pliki wgrywane przez użytkowników
│   ├── campaigns/      # Pliki kampanii reklamowych
│   └── doctors/        # Zdjęcia lekarzy
└── docs/              # Dokumentacja systemu
```

## 🔄 **Zmiany po refaktoryzacji**

### **Główne zmiany:**
1. **Podział na niezależne aplikacje MVC** - każda aplikacja ma własną strukturę
2. **Centralna konfiguracja** - jeden plik `config.php` dla wszystkich aplikacji
3. **Usunięcie duplikacji** - usunięto `/admin/config.php` (zastąpiony globalnym)
4. **Zaktualizowana inicjalizacja bazy** - `init_db.php` zawiera aktualne dane z bazy
5. **Ujednolicone zarządzanie URL-ami** - wszystkie aplikacje używają globalnej konfiguracji

### **Korzyści refaktoryzacji:**
- ✅ Lepsza separacja odpowiedzialności
- ✅ Łatwiejsze utrzymanie kodu
- ✅ Ujednolicona konfiguracja
- ✅ Przygotowanie pod przyszły rozwój
- ✅ Zgodność z wzorcem MVC

---
*Dokumentacja systemu KtoOstatni v2.0.0 (po refaktoryzacji MVC)*
*Ostatnia aktualizacja: 2025-08-29*
