<?php

/**
 * Główny router dla systemu wieloaplikacyjnego KtoOstatni
 * Obsługuje routing do poszczególnych aplikacji: admin, api, display, pwa
 */

// Załaduj konfigurację
require_once __DIR__ . '/config.php';

$request_uri = $_SERVER['REQUEST_URI'];

// Debug - wyświetl informacje o routingu (tymczasowo)
if (isset($_GET['debug'])) {
    echo "Main Router Debug:<br>";
    echo "Original URI: " . $_SERVER['REQUEST_URI'] . "<br>";
    echo "Script Name: " . $_SERVER['SCRIPT_NAME'] . "<br>";
    echo "Current Dir: " . __DIR__ . "<br>";
    echo "Request Method: " . $_SERVER['REQUEST_METHOD'] . "<br>";
    echo "Admin path check: " . (strpos($_SERVER['REQUEST_URI'], '/admin/') === 0 ? 'TRUE' : 'FALSE') . "<br>";
    echo "Admin file exists: " . (file_exists(ADMIN_PATH . '/index.php') ? 'TRUE' : 'FALSE') . "<br>";
    exit;
}

// Routing dla aplikacji admin
if (strpos($request_uri, '/admin/') === 0 || $request_uri === '/admin') {
    $admin_path = ADMIN_PATH . '/index.php';
    if (file_exists($admin_path)) {
        include $admin_path;
        return;
    } else {
        http_response_code(404);
        echo "Aplikacja admin nie została znaleziona.";
        return;
    }
}

// Routing dla API
if (strpos($request_uri, '/api/') === 0 || $request_uri === '/api') {
    $api_path = API_PATH . '/index.php';
    if (file_exists($api_path)) {
        include $api_path;
        return;
    } else {
        http_response_code(404);
        echo "API nie zostało znalezione.";
        return;
    }
}

// Routing dla wyświetlaczy (publiczny dostęp)
if (strpos($request_uri, '/wyswietlacz/') === 0 || strpos($request_uri, '/display/') === 0) {
    $display_path = DISPLAY_PATH . '/index.php';
    if (file_exists($display_path)) {
        include $display_path;
        return;
    } else {
        http_response_code(404);
        echo "Aplikacja wyświetlacza nie została znaleziona.";
        return;
    }
}

// API jest już obsłużone powyżej

// Routing dla aplikacji TV (przyszłość)
if (strpos($request_uri, '/tv/') === 0 || $request_uri === '/tv') {
    // Przekieruj do aplikacji TV (jeszcze nie istnieje)
    http_response_code(404);
    echo "Aplikacja TV jeszcze nie została utworzona.";
    return;
}

// Routing dla aplikacji PWA
if (strpos($request_uri, '/pwa/') === 0) {
    $file_path = PWA_PATH . substr($request_uri, 4); // Usuń /pwa z początku
    if (file_exists($file_path) && is_file($file_path)) {
        $extension = pathinfo($file_path, PATHINFO_EXTENSION);

        // Ustaw odpowiednie nagłówki MIME
        $mime_types = [
            'html' => 'text/html',
            'css' => 'text/css',
            'js' => 'application/javascript',
            'json' => 'application/json',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'webp' => 'image/webp'
        ];

        if (isset($mime_types[$extension])) {
            header('Content-Type: ' . $mime_types[$extension]);
        }

        // Dodaj nagłówki cache dla PWA
        header('Cache-Control: public, max-age=3600');
        header('Expires: ' . gmdate('D, d M Y H:i:s \G\M\T', time() + 3600));

        readfile($file_path);
        return;
    }
}

// Routing dla plików uploads
if (strpos($request_uri, '/uploads/') === 0) {
    $file_path = UPLOADS_PATH . substr($request_uri, 8); // Usuń /uploads z początku
    if (file_exists($file_path) && is_file($file_path)) {
        $extension = pathinfo($file_path, PATHINFO_EXTENSION);

        // Sprawdź czy typ pliku jest dozwolony
        $allowed_extensions = array_merge(
            ALLOWED_IMAGE_TYPES,
            ALLOWED_VIDEO_TYPES,
            ALLOWED_DOCUMENT_TYPES
        );

        if (!in_array(strtolower($extension), $allowed_extensions)) {
            http_response_code(403);
            echo "Niedozwolony typ pliku.";
            return;
        }

        // Ustaw odpowiednie nagłówki MIME
        $mime_types = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'mp4' => 'video/mp4',
            'webm' => 'video/webm',
            'ogv' => 'video/ogg',
            'pdf' => 'application/pdf',
            'csv' => 'text/csv'
        ];

        if (isset($mime_types[$extension])) {
            header('Content-Type: ' . $mime_types[$extension]);
        }

        readfile($file_path);
        return;
    }
}

// Routing dla plików assets (tylko jeśli nie są w aplikacjach)
if (strpos($request_uri, '/assets/') === 0) {
    $file_path = __DIR__ . $request_uri;
    if (file_exists($file_path) && is_file($file_path)) {
        $extension = pathinfo($file_path, PATHINFO_EXTENSION);

        // Ustaw odpowiednie nagłówki MIME
        $mime_types = [
            'css' => 'text/css',
            'js' => 'application/javascript',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'webp' => 'image/webp'
        ];

        if (isset($mime_types[$extension])) {
            header('Content-Type: ' . $mime_types[$extension]);
        }

        readfile($file_path);
        return;
    }
}

// Strona główna - wyświetl listę dostępnych aplikacji
if ($request_uri === '/' || $request_uri === '') {
?>
    <!DOCTYPE html>
    <html lang="pl">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>System Wieloaplikacyjny</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 40px;
            }

            .app {
                margin: 20px 0;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }

            .app h2 {
                margin-top: 0;
            }

            .app a {
                display: inline-block;
                padding: 10px 20px;
                background: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 3px;
            }

            .app a:hover {
                background: #0056b3;
            }
        </style>
    </head>

    <body>
        <h1>System Wieloaplikacyjny</h1>
        <p>Wybierz aplikację, którą chcesz uruchomić:</p>

        <div class="app">
            <h2>Panel Administracyjny</h2>
            <p>Zarządzanie kolejką, reklamami i użytkownikami</p>
            <a href="<?php echo ADMIN_URL; ?>/">Uruchom aplikację</a>
        </div>

        <div class="app">
            <h2>API Systemu</h2>
            <p>Interfejs programistyczny dla integracji z systemem</p>
            <a href="<?php echo API_URL; ?>/">Dokumentacja API</a>
        </div>

        <div class="app">
            <h2>PWA dla Lekarzy</h2>
            <p>Progressive Web App dla lekarzy</p>
            <a href="<?php echo PWA_URL; ?>/">Uruchom aplikację</a>
        </div>

        <div class="app">
            <h2>Wyświetlacz Kolejki</h2>
            <p>Publiczny wyświetlacz systemu kolejkowego</p>
            <a href="<?php echo DISPLAY_URL; ?>/">Przykładowy wyświetlacz</a>
        </div>
    </body>

    </html>
<?php
    return;
}

// Jeśli nie znaleziono żądanej ścieżki
http_response_code(404);
echo "404 - Strona nie została znaleziona.";
?>