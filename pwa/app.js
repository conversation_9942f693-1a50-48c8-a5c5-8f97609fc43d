// Aplikacja PWA dla lekarzy
class DoctorApp {
    constructor() {
        this.currentDoctor = null;
        this.currentRoom = null;
        this.availableRooms = []; // Lista dostępnych gabinetów
        this.currentAppointment = null;
        this.waitingAppointments = [];
        this.updateInterval = null;
        this.timeInterval = null;
        this.isOnline = navigator.onLine;
        this.deferredPrompt = null; // Dla instalacji PWA
        this.apiBaseUrl = '/api'; // Nowy endpoint API
        this.selectedDate = new Date().toISOString().split('T')[0]; // Dzisiejsza data

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkForStoredSession();
        this.setupOnlineOfflineHandling();
        this.initInstallPrompt();
        this.registerServiceWorker();
    }

    setupEventListeners() {
        // Formularz logowania
        const loginForm = document.getElementById('loginForm');
        const accessCodeInput = document.getElementById('accessCode');

        loginForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // Formatowanie kodu dostępu
        accessCodeInput.addEventListener('input', (e) => {
            this.formatAccessCode(e.target);
        });

        // Przyciski akcji
        document.getElementById('nextBtn').addEventListener('click', () => {
            this.callNextAppointment();
        });

        document.getElementById('previousBtn').addEventListener('click', (e) => {
            console.log('=== KLIKNIĘTO PRZYCISK POPRZ ===');
            console.log('Disabled:', e.target.disabled);
            console.log('Current appointment:', this.currentAppointment);
            console.log('Event target:', e.target);

            if (e.target.disabled) {
                console.log('Przycisk jest wyłączony - nie wykonuję akcji');
                return;
            }

            console.log('Wywołuję goToPreviousAppointment...');
            this.goToPreviousAppointment();
        });



        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.logout();
        });

        // Nowe przyciski dla zarządzania gabinetami
        document.getElementById('changeRoomBtn').addEventListener('click', () => {
            this.showRoomSelector();
        });

        document.getElementById('roomSelector').addEventListener('change', (e) => {
            this.changeRoom(e.target.value);
        });

        // Zamykanie modalu wyboru gabinetu
        const roomModal = document.getElementById('roomModal');
        roomModal.addEventListener('click', (e) => {
            if (e.target === roomModal) {
                this.hideRoomSelector();
            }
        });

        document.getElementById('closeRoomModal').addEventListener('click', () => {
            this.hideRoomSelector();
        });

        document.getElementById('closeRoomModalX').addEventListener('click', () => {
            this.hideRoomSelector();
        });

        document.getElementById('confirmRoomChange').addEventListener('click', () => {
            const selector = document.getElementById('roomSelector');
            if (selector.value) {
                this.changeRoom(selector.value);
            }
        });

        // Event listenery dla selektora daty
        document.getElementById('selectedDate').addEventListener('change', (e) => {
            this.changeDate(e.target.value);
        });

        document.getElementById('prevDayBtn').addEventListener('click', () => {
            this.changeDateByDays(-1);
        });

        document.getElementById('nextDayBtn').addEventListener('click', () => {
            this.changeDateByDays(1);
        });

        // Obsługa instalacji PWA
        document.getElementById('installBtn').addEventListener('click', () => {
            this.installApp();
        });
    }

    setupOnlineOfflineHandling() {
        // Nasłuchuj zmian stanu połączenia
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.hideOfflineMessage();
            this.loadAppointments(); // Odśwież dane po powrocie online
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showOfflineMessage();
        });

        // Sprawdź stan połączenia przy starcie
        if (!this.isOnline) {
            this.showOfflineMessage();
        }
    }

    showOfflineMessage() {
        // Usuń istniejące komunikaty
        this.hideOfflineMessage();

        const offlineAlert = document.createElement('div');
        offlineAlert.id = 'offlineAlert';
        offlineAlert.className = 'alert alert-warning alert-dismissible fade show position-fixed';
        offlineAlert.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px;';
        offlineAlert.innerHTML = `
            <i class="fas fa-wifi-slash me-2"></i>
            <strong>Brak połączenia z internetem!</strong> Aplikacja wymaga połączenia z serwerem.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(offlineAlert);
    }

    hideOfflineMessage() {
        const existingAlert = document.getElementById('offlineAlert');
        if (existingAlert) {
            existingAlert.remove();
        }
    }

    formatAccessCode(input) {
        let value = input.value.replace(/[^a-z0-9]/g, '').toLowerCase();

        if (value.length > 12) {
            value = value.substring(0, 12);
        }

        // Formatowanie xxxx-xxxx-xxxx
        if (value.length > 8) {
            value = value.substring(0, 4) + '-' + value.substring(4, 8) + '-' + value.substring(8);
        } else if (value.length > 4) {
            value = value.substring(0, 4) + '-' + value.substring(4);
        }

        input.value = value;
    }

    async handleLogin() {
        console.log('handleLogin wywołane');

        if (!this.isOnline) {
            console.log('Brak połączenia z internetem');
            this.showError('Brak połączenia z internetem. Sprawdź połączenie i spróbuj ponownie.');
            return;
        }

        const accessCode = document.getElementById('accessCode').value.replace(/-/g, '');

        if (accessCode.length !== 12) {
            this.showError('Kod dostępu musi mieć 12 znaków');
            return;
        }

        this.showLoading(true);

        try {
            console.log('Wysyłam żądanie logowania do:', `${this.apiBaseUrl}/doctor/login`);
            console.log('Dane logowania:', { access_code: accessCode });

            const response = await fetch(`${this.apiBaseUrl}/doctor/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ access_code: accessCode })
            });

            console.log('Status odpowiedzi:', response.status);
            console.log('Headers odpowiedzi:', response.headers);

            const data = await response.json();

            console.log('Odpowiedź z API logowania:', data);

            if (data.success) {
                this.currentDoctor = data.data.doctor;
                this.currentRoom = data.data.default_room; // Domyślny gabinet lekarza
                this.availableRooms = data.data.available_rooms || []; // Lista dostępnych gabinetów

                // Zapisz sesję do północy
                const now = new Date();
                const midnight = new Date(now);
                midnight.setHours(24, 0, 0, 0);

                localStorage.setItem('doctorSession', JSON.stringify({
                    doctor: this.currentDoctor,
                    room: this.currentRoom,
                    availableRooms: this.availableRooms,
                    expiresAt: midnight.getTime()
                }));

                this.showMainScreen();
                this.loadAppointments();
                this.startAutoUpdate();

                // Sprawdź czy można pokazać przycisk instalacji
                if (this.deferredPrompt) {
                    this.showInstallButton();
                }
            } else {
                this.showError(data.message || 'Nieprawidłowy kod dostępu');
            }
        } catch (error) {
            console.error('Błąd logowania:', error);
            this.showError('Błąd połączenia z serwerem. Sprawdź połączenie internetowe.');
        } finally {
            this.showLoading(false);
        }
    }

    checkForStoredSession() {
        const stored = localStorage.getItem('doctorSession');
        console.log('Sprawdzam zapisaną sesję:', stored);

        if (stored) {
            try {
                const session = JSON.parse(stored);
                const now = Date.now();

                console.log('Sesja z localStorage:', session);
                console.log('Czas teraz:', now, 'Wygasa:', session.expiresAt);

                // Sprawdź czy sesja nie wygasła (do północy) i czy zawiera wszystkie wymagane dane
                if (session.expiresAt && now < session.expiresAt && session.doctor && session.room) {
                    console.log('Sesja jest ważna, przywracam dane');
                    this.currentDoctor = session.doctor;
                    this.currentRoom = session.room;
                    this.availableRooms = session.availableRooms || [];
                    this.showMainScreen();
                    this.loadAppointments();
                    this.startAutoUpdate();
                } else {
                    console.log('Sesja wygasła lub niepełna, usuwam');
                    localStorage.removeItem('doctorSession');
                }
            } catch (error) {
                console.log('Błąd parsowania sesji:', error);
                localStorage.removeItem('doctorSession');
            }
        } else {
            console.log('Brak zapisanej sesji');
        }
    }

    showMainScreen() {
        document.getElementById('loginScreen').classList.remove('active');
        document.getElementById('mainScreen').classList.add('active');

        this.updateDoctorInfo();
        this.updateRoomSelector();
        this.initializeDateSelector();

        // Dodaj test ładowania danych
        console.log('Pokazuję główny ekran z danymi:', {
            doctor: this.currentDoctor,
            room: this.currentRoom,
            availableRooms: this.availableRooms
        });

        // Załaduj wizyty po pokazaniu ekranu
        setTimeout(() => {
            this.loadAppointments();
        }, 100);
    }

    updateDoctorInfo() {
        if (!this.currentDoctor || !this.currentRoom) return;

        // Informacje o lekarzu
        document.getElementById('doctorName').textContent =
            `Dr. ${this.currentDoctor.first_name} ${this.currentDoctor.last_name}`;

        document.getElementById('roomName').textContent = this.currentRoom.name;

        // Obsługa zdjęcia lekarza
        const photoImg = document.getElementById('doctorPhotoImg');
        const photoIcon = document.getElementById('doctorPhotoIcon');

        // Sprawdź różne możliwe nazwy pól ze zdjęciem
        const photoUrl = this.currentDoctor.photo || this.currentDoctor.photo_url || this.currentDoctor.image;

        console.log('Dane lekarza:', this.currentDoctor);
        console.log('URL zdjęcia:', photoUrl);

        if (photoUrl && photoUrl.trim() !== '') {
            photoImg.src = photoUrl;
            photoImg.style.display = 'block';
            photoIcon.style.display = 'none';
            console.log('Wyświetlam zdjęcie lekarza:', photoUrl);
        } else {
            photoImg.style.display = 'none';
            photoIcon.style.display = 'block';
            console.log('Brak zdjęcia lekarza, wyświetlam ikonę');
        }
    }

    initializeDateSelector() {
        const dateInput = document.getElementById('selectedDate');
        dateInput.value = this.selectedDate;

        // Ustaw minimalną datę na dzisiaj
        const today = new Date().toISOString().split('T')[0];
        dateInput.min = today;
    }

    changeDate(newDate) {
        // Sprawdź czy data nie jest w przeszłości
        const today = new Date().toISOString().split('T')[0];
        if (newDate < today) {
            console.log('Nie można wybrać daty z przeszłości, ustawiam dzisiejszą datę');
            newDate = today;
            document.getElementById('selectedDate').value = newDate;
        }

        this.selectedDate = newDate;
        console.log('Zmiana daty na:', newDate);

        // Odśwież wizyty dla nowej daty
        this.loadAppointments();
    }

    changeDateByDays(days) {
        const currentDate = new Date(this.selectedDate);
        currentDate.setDate(currentDate.getDate() + days);

        // Nie pozwalaj na wybór dat z przeszłości
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (currentDate >= today) {
            const newDateString = currentDate.toISOString().split('T')[0];
            document.getElementById('selectedDate').value = newDateString;
            this.changeDate(newDateString);
        }
    }

    updateRoomSelector() {
        const selector = document.getElementById('roomSelector');
        selector.innerHTML = '';

        // Dodaj opcję "Wybierz gabinet"
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = 'Wybierz gabinet...';
        selector.appendChild(defaultOption);

        // Dodaj dostępne gabinety
        this.availableRooms.forEach(room => {
            const option = document.createElement('option');
            option.value = room.id;
            option.textContent = room.name;
            if (this.currentRoom && room.id === this.currentRoom.id) {
                option.selected = true;
            }
            selector.appendChild(option);
        });
    }

    showRoomSelector() {
        const modal = document.getElementById('roomModal');
        modal.style.display = 'flex';

        // Zaktualizuj listę gabinetów
        this.updateRoomSelector();
    }

    hideRoomSelector() {
        const modal = document.getElementById('roomModal');
        modal.style.display = 'none';
    }

    async changeRoom(roomId) {
        if (!roomId) return;

        const selectedRoom = this.availableRooms.find(room => room.id == roomId);
        if (!selectedRoom) return;

        this.showLoading(true);

        try {
            // Sprawdź czy gabinet jest dostępny dla tego lekarza w tym dniu
            const response = await fetch(`${this.apiBaseUrl}/doctor/check-room-availability`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    doctor_id: this.currentDoctor.id,
                    room_id: roomId,
                    date: new Date().toISOString().split('T')[0] // Dzisiejsza data
                })
            });

            const data = await response.json();

            if (data.success) {
                this.currentRoom = selectedRoom;

                // Zaktualizuj sesję
                const now = new Date();
                const midnight = new Date(now);
                midnight.setHours(24, 0, 0, 0);

                localStorage.setItem('doctorSession', JSON.stringify({
                    doctor: this.currentDoctor,
                    room: this.currentRoom,
                    availableRooms: this.availableRooms,
                    expiresAt: midnight.getTime()
                }));

                this.updateDoctorInfo();
                this.hideRoomSelector();

                // Odśwież wizyty dla nowego gabinetu
                await this.loadAppointments();

                this.showSuccess('Gabinety zmieniony pomyślnie');
            } else {
                this.showError(data.message || 'Nie można zmienić gabinetu');
            }
        } catch (error) {
            console.error('Błąd zmiany gabinetu:', error);
            this.showError('Błąd połączenia z serwerem');
        } finally {
            this.showLoading(false);
        }
    }

    async loadAppointments() {
        if (!this.currentRoom || !this.currentDoctor) {
            console.log('Brak danych o gabinecie lub lekarzu - pomijam ładowanie wizyt');
            return;
        }

        try {
            console.log('Ładowanie wizyt dla:', {
                roomId: this.currentRoom.id,
                doctorId: this.currentDoctor.id,
                date: this.selectedDate
            });

            // Sprawdź wizyty dla wybranej daty
            let dateToCheck = this.selectedDate;
            let foundAppointments = false;
            let attempts = 0;
            const maxAttempts = 3; // Sprawdź dzisiaj, jutro i pojutrze

            while (!foundAppointments && attempts < maxAttempts) {
                const response = await fetch(`${this.apiBaseUrl}/doctor/appointments/${this.currentRoom.id}?doctor_id=${this.currentDoctor.id}&date=${dateToCheck}`);
                const data = await response.json();

                console.log(`Sprawdzanie wizyt dla daty ${dateToCheck}:`, data);

                if (data.success) {
                    const hasCurrentAppointment = data.data.current !== null;
                    const hasWaitingAppointments = data.data.waiting && data.data.waiting.length > 0;

                    if (hasCurrentAppointment || hasWaitingAppointments) {
                        // Znaleziono wizyty - użyj tej daty
                        foundAppointments = true;

                        // Jeśli znaleziono wizyty na inną datę niż wybrana, zaktualizuj wybraną datę
                        if (dateToCheck !== this.selectedDate) {
                            console.log(`Znaleziono wizyty na datę ${dateToCheck}, przełączam z ${this.selectedDate}`);
                            this.selectedDate = dateToCheck;
                            document.getElementById('selectedDate').value = dateToCheck;
                        }

                        this.currentAppointment = data.data.current || null;
                        this.waitingAppointments = data.data.waiting || [];

                        // Użyj wszystkich wizyt z API i oznacz która jest aktywna
                        this.allAppointments = (data.data.all_appointments || []).map(apt => ({
                            ...apt,
                            isActive: this.currentAppointment && apt.id === this.currentAppointment.id
                        }));

                        // Sortuj wszystkie wizyty według czasu (już posortowane z API, ale dla pewności)
                        this.allAppointments.sort((a, b) => a.appointment_time.localeCompare(b.appointment_time));

                        console.log('Zaktualizowane dane:', {
                            currentAppointment: this.currentAppointment,
                            waitingCount: this.waitingAppointments.length,
                            allAppointments: this.allAppointments.length,
                            selectedDate: this.selectedDate
                        });

                        this.updateAppointmentDisplay();
                        this.updateWaitingList();
                        await this.updateStats();
                    } else {
                        // Brak wizyt na tę datę, sprawdź następny dzień
                        attempts++;
                        if (attempts < maxAttempts) {
                            const nextDate = new Date(dateToCheck);
                            nextDate.setDate(nextDate.getDate() + 1);
                            dateToCheck = nextDate.toISOString().split('T')[0];
                            console.log(`Brak wizyt na ${dateToCheck}, sprawdzam następny dzień: ${dateToCheck}`);
                        }
                    }
                } else {
                    console.error('Błąd ładowania wizyt:', data.message);
                    break;
                }
            }

            // Jeśli nie znaleziono żadnych wizyt w ciągu 3 dni
            if (!foundAppointments) {
                console.log('Nie znaleziono wizyt w ciągu najbliższych 3 dni');
                this.currentAppointment = null;
                this.waitingAppointments = [];
                this.allAppointments = [];
                this.updateAppointmentDisplay();
                this.updateWaitingList();
                await this.updateStats();
            }

        } catch (error) {
            console.error('Błąd połączenia podczas ładowania wizyt:', error);
        }
    }

    updateAppointmentDisplay() {
        const timeElement = document.getElementById('currentAppointmentTime');
        const nameElement = document.getElementById('currentPatientName');
        const nextBtn = document.getElementById('nextBtn');
        const previousBtn = document.getElementById('previousBtn');

        console.log('updateAppointmentDisplay:', {
            hasCurrentAppointment: !!this.currentAppointment,
            currentAppointment: this.currentAppointment,
            waitingCount: this.waitingAppointments.length
        });

        if (this.currentAppointment) {
            timeElement.textContent = this.currentAppointment.appointment_time;
            nameElement.textContent = this.currentAppointment.patient_name || 'Pacjent';

            // Przycisk "Następna" - nawigacja do następnej wizyty
            nextBtn.disabled = false;
            nextBtn.innerHTML = '<i class="fas fa-arrow-right"></i> Następna';
            nextBtn.title = 'Przejdź do następnej wizyty';

            // Przycisk "Poprzednia" - nawigacja do poprzedniej wizyty
            previousBtn.disabled = false;
            previousBtn.title = 'Przejdź do poprzedniej wizyty';

            console.log('Przycisk Poprz. ustawiony jako aktywny');
        } else {
            timeElement.textContent = '--:--';
            nameElement.textContent = 'Brak aktualnej wizyty';

            // Przycisk "Następna" - aktywny gdy są oczekujące wizyty
            nextBtn.disabled = this.waitingAppointments.length === 0;
            nextBtn.innerHTML = '<i class="fas fa-arrow-right"></i> Następna';
            if (this.waitingAppointments.length === 0) {
                nextBtn.title = 'Brak oczekujących wizyt';
            } else {
                nextBtn.title = 'Wywołaj pierwszą wizytę';
            }

            // Przycisk "Poprzednia" - wyłączony gdy nie ma aktualnej wizyty
            previousBtn.disabled = true;
            previousBtn.title = 'Brak aktualnej wizyty';

            console.log('Przycisk Poprz. ustawiony jako nieaktywny');
        }

        // Dodaj wizualne wskazówki o stanie przycisków
        if (nextBtn.disabled) {
            nextBtn.classList.add('btn-secondary');
            nextBtn.classList.remove('btn-success');
        } else {
            nextBtn.classList.remove('btn-secondary');
            nextBtn.classList.add('btn-success');
        }

        if (previousBtn.disabled) {
            previousBtn.classList.add('btn-secondary');
            previousBtn.classList.remove('btn-outline-secondary');
        } else {
            previousBtn.classList.remove('btn-secondary');
            previousBtn.classList.add('btn-outline-secondary');
        }

        console.log('Stan przycisków po aktualizacji:', {
            nextBtnDisabled: nextBtn.disabled,
            previousBtnDisabled: previousBtn.disabled,
            previousBtnClasses: previousBtn.className
        });
    }

    updateWaitingList() {
        const container = document.getElementById('waitingList');

        if (!this.allAppointments || this.allAppointments.length === 0) {
            container.innerHTML = `
                <div class="no-appointments">
                    <i class="fas fa-inbox"></i>
                    <p>Brak wizyt</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.allAppointments.map((appointment) => {
            let cssClass = '';
            if (appointment.isActive) {
                cssClass = 'active';
            } else if (appointment.status === 'closed') {
                cssClass = 'closed';
            }

            return `
                <div class="waiting-item ${cssClass}" data-id="${appointment.id}">
                    <div class="waiting-time">${appointment.appointment_time}</div>
                    <div class="waiting-patient">${appointment.patient_name || 'Pacjent'}</div>
                </div>
            `;
        }).join('');
    }

    async updateStats() {
        const completedCount = document.getElementById('completedCount');
        const waitingCount = document.getElementById('waitingCount');

        if (!this.allAppointments || this.allAppointments.length === 0) {
            completedCount.textContent = '0';
            waitingCount.textContent = '0';
            return;
        }

        // Oblicz statystyki na podstawie aktualnej pozycji w liście
        let completed = 0;
        let waiting = 0;
        let foundCurrent = false;

        for (const appointment of this.allAppointments) {
            if (appointment.isActive) {
                foundCurrent = true;
                // Aktualna wizyta nie jest liczona ani jako zakończona ani jako oczekująca
            } else if (!foundCurrent) {
                // Wizyty przed aktualną są traktowane jako zakończone
                completed++;
            } else {
                // Wizyty po aktualnej są oczekujące
                waiting++;
            }
        }

        // Jeśli nie ma aktualnej wizyty, wszystkie są oczekujące
        if (!foundCurrent) {
            waiting = this.allAppointments.length;
            completed = 0;
        }

        completedCount.textContent = completed;
        waitingCount.textContent = waiting;

        console.log('Zaktualizowane statystyki:', {
            completed,
            waiting,
            total: this.allAppointments.length,
            hasCurrentAppointment: foundCurrent
        });
    }

    async callNextAppointment() {
        console.log('Nawigacja do następnej wizyty...', {
            isOnline: this.isOnline,
            currentRoom: this.currentRoom,
            currentDoctor: this.currentDoctor,
            waitingCount: this.waitingAppointments.length,
            hasCurrentAppointment: !!this.currentAppointment
        });

        if (!this.isOnline) {
            this.showError('Brak połączenia z internetem. Sprawdź połączenie i spróbuj ponownie.');
            return;
        }

        if (!this.currentRoom || !this.currentDoctor) {
            this.showError('Brak danych o gabinecie lub lekarzu. Spróbuj się zalogować ponownie.');
            return;
        }

        this.showLoading(true);

        try {
            // Jeśli nie ma aktualnej wizyty, wywołaj pierwszą oczekującą
            if (!this.currentAppointment) {
                if (this.waitingAppointments.length === 0) {
                    this.showError('Brak oczekujących wizyt do wywołania.');
                    return;
                }

                console.log('Brak aktualnej wizyty - wywołuję pierwszą oczekującą...');
                const response = await fetch(`${this.apiBaseUrl}/doctor/call-next/${this.currentRoom.id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        doctor_id: this.currentDoctor.id
                    })
                });

                const data = await response.json();
                if (data.success) {
                    this.showSuccess('Pierwsza wizyta została wywołana!');
                } else {
                    this.showError(data.message || 'Błąd podczas wywołania wizyty');
                }
            } else {
                // Jeśli jest aktualna wizyta, zakończ ją i wywołaj następną
                console.log('Kończenie aktualnej wizyty i wywołanie następnej...');
                const response = await fetch(`${this.apiBaseUrl}/doctor/call-next/${this.currentRoom.id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        doctor_id: this.currentDoctor.id
                    })
                });

                const data = await response.json();
                if (data.success) {
                    this.showSuccess('Aktualna wizyta zakończona. Wywołano następną wizytę.');
                } else {
                    if (response.status === 404) {
                        this.showError('Brak następnej wizyty');
                    } else {
                        this.showError(data.message || 'Błąd podczas wywołania następnej wizyty');
                    }
                }
            }

            // Odśwież dane
            await this.loadAppointments();
            // Odśwież statystyki
            await this.updateStats();
            // Animacja przejścia
            this.animateTransition();
        } catch (error) {
            console.error('Błąd nawigacji wizyty:', error);
            this.showError('Błąd połączenia z serwerem. Sprawdź połączenie internetowe.');
        } finally {
            this.showLoading(false);
        }
    }

    async completeCurrentAppointment() {
        if (!this.currentAppointment) {
            console.log('Brak aktualnej wizyty do zakończenia');
            return;
        }

        this.showLoading(true);

        try {
            console.log('Kończenie aktualnej wizyty przez skip-current...');

            const response = await fetch(`${this.apiBaseUrl}/doctor/skip-current/${this.currentRoom.id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    doctor_id: this.currentDoctor.id
                })
            });

            const data = await response.json();
            console.log('Odpowiedź zakończenia wizyty:', data);

            if (data.success) {
                this.showSuccess('Aktualna wizyta została zakończona. Kliknij ponownie aby wywołać następną.');
                // Odśwież dane
                await this.loadAppointments();
                // Odśwież statystyki
                await this.updateStats();
            } else {
                this.showError(data.message || 'Błąd podczas kończenia wizyty');
            }
        } catch (error) {
            console.error('Błąd kończenia wizyty:', error);
            this.showError('Błąd połączenia z serwerem. Sprawdź połączenie internetowe.');
        } finally {
            this.showLoading(false);
        }
    }

    // Funkcja pomocnicza do cichego kończenia wizyty (bez komunikatów)
    async completeCurrentAppointmentSilent() {
        if (!this.currentAppointment) {
            return;
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}/doctor/skip-current/${this.currentRoom.id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    doctor_id: this.currentDoctor.id
                })
            });

            const data = await response.json();

            if (!data.success) {
                console.error('Błąd podczas cichego kończenia wizyty:', data.message);
            }
        } catch (error) {
            console.error('Błąd cichego kończenia wizyty:', error);
        }
    }

    async goToPreviousAppointment() {
        console.log('Nawigacja do poprzedniej wizyty...', {
            isOnline: this.isOnline,
            currentRoom: this.currentRoom,
            currentDoctor: this.currentDoctor,
            hasCurrentAppointment: !!this.currentAppointment
        });

        if (!this.isOnline) {
            this.showError('Brak połączenia z internetem. Sprawdź połączenie i spróbuj ponownie.');
            return;
        }

        if (!this.currentRoom || !this.currentDoctor) {
            this.showError('Brak danych o gabinecie lub lekarzu. Spróbuj się zalogować ponownie.');
            return;
        }

        if (!this.currentAppointment) {
            this.showError('Brak aktualnej wizyty.');
            return;
        }

        this.showLoading(true);

        try {
            console.log('Wysyłanie żądania do:', `/api/doctor/navigate-previous/${this.currentRoom.id}`);
            console.log('Dane:', {
                doctor_id: this.currentDoctor.id,
                date: this.selectedDate
            });

            const response = await fetch(`${this.apiBaseUrl}/doctor/navigate-previous/${this.currentRoom.id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    doctor_id: this.currentDoctor.id,
                    date: this.selectedDate
                })
            });

            console.log('Odpowiedź serwera:', response.status, response.statusText);
            const data = await response.json();
            console.log('Dane odpowiedzi:', data);

            if (data.success) {
                this.showSuccess('Przeszedłeś do poprzedniej wizyty');
                await this.loadAppointments();
                await this.updateStats();
                this.animateTransition();
            } else {
                console.log('Błąd API navigate-previous:', data);
                if (response.status === 404) {
                    this.showError('Brak poprzedniej wizyty');
                } else {
                    this.showError(data.message || 'Błąd podczas nawigacji do poprzedniej wizyty');
                }
            }
        } catch (error) {
            console.error('Błąd nawigacji do poprzedniej wizyty:', error);
            this.showError('Błąd połączenia z serwerem. Sprawdź połączenie internetowe.');
        } finally {
            this.showLoading(false);
        }
    }

    animateTransition() {
        const appointmentDisplay = document.querySelector('.current-appointment');
        if (appointmentDisplay) {
            appointmentDisplay.classList.add('fade-in');

            setTimeout(() => {
                appointmentDisplay.classList.remove('fade-in');
            }, 500);
        }
    }

    startAutoUpdate() {
        this.updateInterval = setInterval(() => {
            if (this.isOnline) {
                this.loadAppointments();
            }
        }, 10000); // Odśwież co 10 sekund tylko gdy online
    }

    logout() {
        // Wyczyść localStorage
        localStorage.removeItem('accessCode');
        localStorage.removeItem('doctorSession');

        // Zatrzymaj auto-update
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }

        // Wyczyść dane
        this.currentDoctor = null;
        this.currentRoom = null;
        this.availableRooms = [];
        this.currentAppointment = null;
        this.waitingAppointments = [];

        // Ukryj przycisk instalacji
        this.hideInstallButton();

        // Wyczyść formularz logowania
        document.getElementById('accessCode').value = '';

        // Pokaż ekran logowania
        document.getElementById('mainScreen').classList.remove('active');
        document.getElementById('loginScreen').classList.add('active');
    }

    showLoading(show) {
        const spinner = document.getElementById('loadingSpinner');
        spinner.style.display = show ? 'flex' : 'none';
    }

    showError(message) {
        const errorElement = document.getElementById('loginError');
        const messageElement = document.getElementById('errorMessage');

        messageElement.textContent = message;
        errorElement.style.display = 'block';

        // Ukryj po 5 sekundach
        setTimeout(() => {
            this.hideError();
        }, 5000);
    }

    showSuccess(message) {
        // Utwórz tymczasowy komunikat sukcesu
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        successAlert.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px;';
        successAlert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            <strong>Sukces!</strong> ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        document.body.appendChild(successAlert);

        // Usuń po 3 sekundach
        setTimeout(() => {
            if (successAlert.parentElement) {
                successAlert.remove();
            }
        }, 3000);
    }

    hideError() {
        const errorElement = document.getElementById('loginError');
        errorElement.style.display = 'none';
    }

    initInstallPrompt() {
        // Sprawdź czy przeglądarka obsługuje instalację PWA
        if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
            console.log('PWA: Przeglądarka nie obsługuje PWA');
            return;
        }

        // Poproś o uprawnienia do powiadomień
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }

        // Nasłuchuj na event beforeinstallprompt
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA: beforeinstallprompt event fired');
            // Zapobiegaj domyślnemu wyświetleniu promptu
            e.preventDefault();
            // Zapisz event do późniejszego użycia
            this.deferredPrompt = e;
            // Pokaż przycisk instalacji tylko na ekranie głównym
            if (document.getElementById('mainScreen').classList.contains('active')) {
                this.showInstallButton();
            }
        });

        // Nasłuchuj na event appinstalled
        window.addEventListener('appinstalled', (evt) => {
            console.log('PWA: Aplikacja została zainstalowana');
            this.hideInstallButton();
            this.deferredPrompt = null;

            // Pokaż powiadomienie o sukcesie
            this.showNotification('Aplikacja została zainstalowana! Możesz teraz uruchomić ją z pulpitu.', 'success');

            // Opcjonalnie: przekieruj do zainstalowanej aplikacji
            setTimeout(() => {
                if (window.matchMedia('(display-mode: standalone)').matches) {
                    window.location.reload();
                }
            }, 2000);
        });

        // Sprawdź czy aplikacja jest już zainstalowana
        if (window.matchMedia('(display-mode: standalone)').matches ||
            window.navigator.standalone === true ||
            document.referrer.includes('android-app://')) {
            console.log('PWA: Aplikacja już zainstalowana');
            this.hideInstallButton();
            return;
        }

        // Sprawdź czy przeglądarka obsługuje instalację
        if (!window.deferredPrompt) {
            console.log('PWA: Przeglądarka nie obsługuje instalacji PWA');
            this.hideInstallButton();
        } else {
            console.log('PWA: Przeglądarka obsługuje instalację PWA');
        }
    }

    showInstallButton() {
        // Sprawdź czy aplikacja nie jest już zainstalowana
        if (window.matchMedia('(display-mode: standalone)').matches ||
            window.navigator.standalone === true ||
            document.referrer.includes('android-app://')) {
            console.log('PWA: Aplikacja już zainstalowana - ukrywam przycisk');
            this.hideInstallButton();
            return;
        }

        const installBtn = document.getElementById('installBtn');
        if (installBtn) {
            installBtn.classList.add('show');
            console.log('PWA: Pokazuję przycisk instalacji');
        }
    }

    hideInstallButton() {
        const installBtn = document.getElementById('installBtn');
        if (installBtn) {
            installBtn.classList.remove('show');
            console.log('PWA: Ukrywam przycisk instalacji');
        }
    }

    async installApp() {
        if (!this.deferredPrompt) {
            console.log('PWA: Brak promptu instalacji');

            // Sprawdź czy to Chrome/Edge
            const isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);
            const isEdge = /Edg/.test(navigator.userAgent);

            if (isChrome || isEdge) {
                this.showNotification('Kliknij ikonę instalacji w pasku adresu przeglądarki', 'info');
            } else {
                this.showNotification('Aplikacja nie może być zainstalowana w tej przeglądarce. Użyj Chrome lub Edge.', 'warning');
            }
            return;
        }

        try {
            // Pokaż prompt instalacji
            this.deferredPrompt.prompt();

            // Czekaj na odpowiedź użytkownika
            const { outcome } = await this.deferredPrompt.userChoice;

            console.log(`PWA: Użytkownik ${outcome === 'accepted' ? 'zaakceptował' : 'odrzucił'} instalację`);

            if (outcome === 'accepted') {
                this.showNotification('Instalacja w toku...', 'info');
            } else {
                this.showNotification('Instalacja została anulowana', 'info');
            }

            // Wyczyść prompt
            this.deferredPrompt = null;

            // Ukryj przycisk
            this.hideInstallButton();

        } catch (error) {
            console.error('PWA: Błąd podczas instalacji:', error);
            this.showNotification('Wystąpił błąd podczas instalacji', 'error');
        }
    }

    registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/pwa/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);

                        // Sprawdź czy Service Worker jest aktywny
                        if (registration.active) {
                            console.log('SW is active');
                        }

                        // Nasłuchuj na aktualizacje Service Worker
                        registration.addEventListener('updatefound', () => {
                            console.log('SW update found');
                        });
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        } else {
            console.log('PWA: Service Worker nie jest obsługiwany');
        }
    }

    showNotification(message, type = 'info') {
        // Sprawdź czy przeglądarka obsługuje powiadomienia
        if (!('Notification' in window)) {
            console.log('PWA: Przeglądarka nie obsługuje powiadomień');
            return;
        }

        // Ustaw ikonę na podstawie typu
        let icon = '/pwa/icons/icon-192x192.png';
        let title = 'KtoOstatni';

        switch (type) {
            case 'success':
                title = '✅ ' + title;
                break;
            case 'error':
                title = '❌ ' + title;
                break;
            case 'warning':
                title = '⚠️ ' + title;
                break;
            default:
                title = 'ℹ️ ' + title;
        }

        // Sprawdź uprawnienia
        if (Notification.permission === 'granted') {
            new Notification(title, {
                body: message,
                icon: icon,
                badge: icon,
                tag: 'doctor-panel-notification',
                requireInteraction: type === 'error' || type === 'warning'
            });
        } else if (Notification.permission !== 'denied') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    new Notification(title, {
                        body: message,
                        icon: icon,
                        badge: icon,
                        tag: 'doctor-panel-notification',
                        requireInteraction: type === 'error' || type === 'warning'
                    });
                }
            });
        }
    }
}

// Inicjalizacja aplikacji
document.addEventListener('DOMContentLoaded', () => {
    new DoctorApp();
});

// Service Worker registration
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/pwa/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
} 